export type GetStoreDetailsInput = {
  storeId: string;
  brandId?: string | null;
  provisionType?: string;
  credentialType?: string | null;
};

export type StoreDetailsResult = {
  clientid: string;
  clientsecret: string;
  username: string;
  upassword: string;
  granttype: string;
  resource: string;
  brand: string;
  markvalue: number;
  storedomain: string;
  token: string;
  redirecturi: string;
};

export type StatusResponse = {
  status: "PASS" | "FAIL"
  message?: string
}

export interface ContractDetail {
  qty: number;
  discount_percentage: number;
  discounted_unit_price: number;
  sub_total: number;
  vat_percentage: number;
  promotion_percentage: number;
  net_price: number;
  unit_price_after_promotion: number;
  unit_price_before_promotion: number;
  subscription_id: string;
  promotion_id: string | null;
  promotion_start_date?: string | null;
  promotion_end_date?: string | null;
}

export interface LastContractInfoDto {
  data: ContractDetail;
}

// Raw data models from database
export interface ContractDetailRaw {
  discount: number
  unitprice: number
  vat: number
  statusreason: string
}

export interface ContractPromotionModel {
  PromotionStartDate: string;
  PromotionEndDate: string;
}

export interface LastContractInfoModel {
  Quantity: number;
  contractDetail: ContractDetailRaw;
  PromotionIdDuringOrder: string;
  PromotionRateLastRenewal: string;
  PromotionValue: number;
  PromotionIdLastRenewal: string;
  promotionModel?: ContractPromotionModel;
}

export interface CreateCustomerStatusResponse extends StatusResponse {
  UserName?: string
  Password?: string
}

export type CustomerValidationStatusResponse = {
  status: boolean
  message: string
}

export type GetCustomerUsersResponse = {
  TotalCount: number;
  Items: any[];
  Links: any;
  Attributes: any;
  Code: number;
  Description: string;
  status?: boolean;
};

export type GetCustomerBillingInfoResponse = {
  id: string;
  email: string;
  culture: string;
  language: string;
  companyName: string;
  defaultAddress: any;
  links: any;
  attributes: any;
  firstName: string;
  lastName: string;
  code: number;
  description: string;
  status?: boolean;
};

export type GetCustomerAgreement = {
  totalCount: number;
  items: any[];
};

export type ErrorResponseModel = {
  errors: string[];
  statusCode: number;
};

export type GetCustomerAgreementServiceResponse = {
  getCustomerAgreement?: GetCustomerAgreement;
  error?: ErrorResponseModel;
};

// Equivalent of GetCustomerByIdMaster
export interface GetCustomerByIdMaster {
  GetCustomerById: GetCustomerById
  Error?: ErrorResponseModel
}

// Equivalent of GetCustomerById
export interface GetCustomerById {
  Status: string
  Message: string
}

// Equivalent of GetCustomersFilter
export interface GetCustomersFilter {
  Field: string
  Value: string
  Operator: string
}

// Factory function to create default object
export function createGetCustomersFilter(value: string): GetCustomersFilter {
  return {
    Field: "Domain",
    Value: value,
    Operator: "starts_with",
  }
}

// Equivalent of GetCustomerByIdResponse
export interface GetCustomerByIdResponse {
  Id: string
  CompanyProfile: CompanyProfile
  RelationShipToPartner: string
  Links: Links
  Atrributes: Attributes
  CommerceId: string
  BillingProfile: BillingProfile
  AllowDelegatedAccess: boolean
  CustomDomains: string[]
  Code: number
  Description: string
  UserCredentials: UserCredentials
}

// ---------------------- Nested Types ----------------------

export interface CompanyProfile {
  TenantId: string
  Domain: string
  CompanyName: string
  Links: Links
  Attributes: Attributes
  Address: Address
  Email: string
}

export interface Address {
  Country: string
  City: string
  Region: string
  AddressLine1: string
  PostalCode: string
  PhoneNumber: string
  AddressLine2: string
  FirstName: string
  LastName: string
  State: string
}

export interface Links {
  Self?: LinkDetail
  Offer?: LinkDetail
  Next?: LinkDetail
  Availabilities?: LinkDetail
  Skus?: LinkDetail
}

export interface LinkDetail {
  Uri: string
  Method: string
  headers?: string[] // Only Self has headers in original .NET model
}

export interface Attributes {
  ObjectType: string
  Etag: string
}

export interface BillingProfile {
  Id: string
  Email: string
  Culture: string
  Language: string
  CompanyName: string
  DefaultAddress: DefaultAddress
  Links: Links
  Attributes: Attributes
  FirstName: string
  LastName: string
  Code: number
  Description: string
}

export interface DefaultAddress {
  Country: string
  City: string
  AddressLine1: string
  PostalCode: string
  PhoneNumber: string
  AddressLine2: string
  FirstName: string
  LastName: string
  State: string
}

export interface UserCredentials {
  UserName: string
  Password: string
}

export interface CheckDomainResponse {
  Status: string
  Message: string
}

export type GetDirectSignStatusOfCustAgreementResponse = {
  status: "PASS" | "FAIL";
  message: string;
  isSigned?: boolean;
};

export type SetCustomerAgreementResponse = {
  status: boolean;
  message: string;
  data?: any;
};

export type SetCustomerAgreementServiceResponse = {
  setCustomerAgreement?: SetCustomerAgreementResponse;
  error?: ErrorResponseModel;
};

export type GetMicAgreementItem = {
  templateId: string;
  agreementType: string;
  agreementLink: string;
  versionRank: number;
};

export type GetMicAgreementResponse = {
  totalCount: number;
  items: GetMicAgreementItem[];
};

// DocuSign OAuth token response
export type DocuSignOAuthToken = {
  access_token: string;
  token_type: string;
  refresh_token?: string;
  expires_in?: number;
};

// DocuSign service response
export type DocuSignTokenServiceResponse = {
  success: boolean;
  data?: DocuSignOAuthToken;
  error?: string;
};

// DocuSign configuration
export type DocuSignConfig = {
  clientId: string;
  userId: string;
  oauthBasePath: string;
  keyFilePath: string;
};

// Microsoft API Response (matching .NET MicrosoftApiResponse)
export type MicrosoftApiResponse = {
  message: string;
  data?: any;
  statusCode: number;
  isError: boolean;
};

// Microsoft API Response with Response Type (matching .NET MicrosoftApiResponseWithResponseType)
export type MicrosoftApiResponseWithResponseType = MicrosoftApiResponse & {
  type?: string;
};

// Azure Billed Usage DTO
export type AzureBilledUsageDto = {
  storeId: string;
  brandId: string;
  invoiceId: string;
  attributeSet: string;
};

// Azure Billed Usage Request (for service layer)
export type AzureBilledUsageRequest = {
  invoiceId: string;
  attributeSet: string;
};

// Azure Service Response with headers
export type AzureServiceResponse = {
  response: MicrosoftApiResponse;
  headers?: Record<string, string>;
};

// Provisional Status Response (matching .NET ProvisionalStatusResponse)
export type ProvisionalStatusResponse = {
  totalCount: number;
  items: ProvisionalStatusItem[];
};

export type ProvisionalStatusItem = {
  status: string;
};

// Entitlement types (matching .NET classes)
export type Entitlement = {
  includedEntitlements?: Entitlement[];
  referenceOrder?: ReferenceOrder;
  productId?: string;
  quantity?: number;
  quantityDetails?: QuantityDetail[];
  entitledArtifacts?: Artifact[];
  skuId?: string;
  entitlementType?: string;
  fulfillmentState?: string;
  expiryDate?: string; // ISO date string
  subscriptionId?: string;
  hasAddOn?: boolean;
  subscriptionStatus?: string;
  startDate?: string; // ISO date string
};

export type ReferenceOrder = {
  id?: string;
  alternateId?: string;
};

export type QuantityDetail = {
  quantity?: number;
  state?: string;
};

export type Artifact = {
  artifactType?: string;
  dynamicAttributes?: Record<string, any>;
};

// Order Detail types (matching .NET OrderDetail)
export type OrderDetail = {
  Id?: string; // Changed to match .NET casing
  AlternateId?: string;
  ReferenceCustomerId?: string;
  Status?: string;
  LineItems?: OrderLineItem[];
};

export type OrderLineItem = {
  LineItemNumber?: number; // Changed to match .NET casing
  OfferId?: string;
  SubscriptionId?: string;
  PartnerIdOnRecord?: string;
  Quantity?: number;
  FriendlyName?: string;
  TermDuration?: string;
};

// Subscription Response types (matching .NET GetSubscriptionResponse)
export type GetSubscriptionResponse = {
  Id?: string; // Changed to match .NET casing
  OfferId?: string;
  OfferName?: string;
  FriendlyName?: string;
  Quantity?: number;
  UnitType?: string;
  HasPurchasableAddons?: boolean;
  CreationDate?: string; // ISO date string
  EffectiveStartDate?: string; // ISO date string
  CommitmentEndDate?: string; // ISO date string
  CancellationAllowedUntilDate?: string; // ISO date string
  Status?: string;
  AutoRenewEnabled?: boolean;
  IsTrial?: boolean;
  BillingType?: string;
  BillingCycle?: string;
  TermDuration?: string;
  RenewalTermDuration?: string;
  IsMicrosoftProduct?: boolean;
  PartnerId?: string;
  AttentionNeeded?: boolean;
  ActionTaken?: boolean;
  ContractType?: string;
  PublisherName?: string;
  PromotionId?: string;
  OrderId?: string;
  ErrorMessage?: string;
  ScheduledNextTermInstructions?: ScheduledNextTermInstructionsModel;
};

export type ScheduledNextTermInstructionsModel = {
  Quantity?: number; // Changed to match .NET casing
  Product?: ProductModel;
  CustomTermEndDate?: string; // ISO date string
};

export type ProductModel = {
  ProductId?: string; // Changed to match .NET casing
  SkuId?: string;
  AvailabilityId?: string;
  BillingCycle?: string;
  TermDuration?: string;
  PromotionId?: string;
};

// Token Request type (matching .NET TokenRequest)
export type TokenRequest = {
  grant_type: string;
  client_id: string;
  client_secret: string;
  resource: string;
  refresh_token: string;
  redirect_uri: string;
  store_domain: string;
  brand?: string;
  markValue?: number;
};

// Token Response type
export type TokenResponse = {
  access_token: string;
  token_type?: string;
  expires_in?: number;
  refresh_token?: string;
};

// Response Error type
export type ResponseError = {
  description: string;
  code?: string;
};

// Response Status type (matching .NET ResponseStatus)
export interface ResponseStatus {
  Status: boolean;
  Message?: string;
}

// Microsoft Validation Request (matching .NET MicrosoftValidationRequest)
export interface MicrosoftValidationRequest {
  CustomerTenantId?: string;
  MPNId?: string;
  PromotionEligibilityRequests: PromotionEligibilityRequest[];
}

// Promotion Eligibility Request (matching .NET PromotionEligibilityRequest)
export interface PromotionEligibilityRequest {
  Id?: number | null;
  CatalogItemId?: string;
  Quantity?: number;
  TermDuration?: string;
  BillingCycle?: string;
  PromotionId?: string;
  MaterialId?: string;
  PromotionDescription?: string;
}

// Check MPNID Response (matching .NET CheckMPNIDResponse)
export interface CheckMPNIDResponse {
  PartnerName?: string;
  MpnId?: string;
  ProfileType?: string;
}

// Google Create Customer Request (matching .NET GoogleCreateCustomerRequest)
export interface GoogleCreateCustomerRequest {
  OrgDisplayName?: string;
  OrgPostalAddress?: OrgPostalAddressRequest;
  PrimaryContactInfo?: PrimaryContactInfoRequest;
  AlternateEmail?: string;
  Domain?: string;
  LanguageCode?: string;
  CloudIdentityInfo?: CloudIdentityInfoRequest;
  ChannelPartnerId?: string;
  CorrelationId?: string;
}

// Org Postal Address Request (matching .NET OrgPostalAddressRequest)
export interface OrgPostalAddressRequest {
  Revision?: number;
  RegionCode?: string;
  LanguageCode?: string;
  PostalCode?: string;
  SortingCode?: any;
  AdministrativeArea?: string;
  Locality?: any;
  Sublocality?: any;
  AddressLines?: string[];
  Recipients?: string[];
  Organization?: string;
}

// Primary Contact Info Request (matching .NET PrimaryContactInfoRequest)
export interface PrimaryContactInfoRequest {
  FirstName?: string;
  LastName?: string;
  DisplayName?: string;
  Email?: string;
  Title?: string;
  Phone?: string;
}

// Cloud Identity Info Request (matching .NET CloudIdentityInfoRequest)
export interface CloudIdentityInfoRequest {
  CustomerType?: string;
  PrimaryDomain?: string;
  IsDomainVerified?: string;
  AlternateEmail?: string;
  PhoneNumber?: string;
  LanguageCode?: string;
  AdminConsoleUri?: any;
  EduData?: EduDataRequest;
}

// Edu Data Request (matching .NET EduDataRequest)
export interface EduDataRequest {
  InstituteType?: string;
  InstituteSize?: string;
  Website?: string;
}

// Google Create Customer Response (matching .NET GoogleCreateCustomerResponse)
export interface GoogleCreateCustomerResponse {
  Name?: string;
  CustomerId?: string;
  [key: string]: any; // Allow for additional dynamic properties from Google API
}

// Google Create Customer DTO (matching .NET GoogleCreateCustomerDto)
export interface GoogleCreateCustomerDto {
  custId?: string; // Internal field, set from route params
  partnerId?: string; // Internal field, set from route params
  brandIds: string[];
  storeId?: string; // Internal field, set from route params
  orgDisplayName?: string;
  orgPostalAddress?: OrgPostalAddressDto;
  primaryContactInfo?: PrimaryContactInfoDto;
  alternateEmail?: string;
  domain?: string;
  languageCode?: string;
  cloudIdentityInfo?: CloudIdentityInfoDto;
  channelPartnerId?: string;
  correlationId?: string;
  googleCustomerId?: string;
  cloudIdentityId?: string;
}

// Org Postal Address DTO (matching .NET OrgPostalAddressDto)
export interface OrgPostalAddressDto {
  revision?: number;
  regionCode?: string;
  languageCode?: string;
  postalCode?: string;
  sortingCode?: any;
  administrativeArea?: string;
  locality?: any;
  sublocality?: any;
  addressLines?: string[];
  recipients?: string[];
  organization?: string;
}

// Primary Contact Info DTO (matching .NET PrimaryContactInfoDto)
export interface PrimaryContactInfoDto {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  email?: string;
  title?: string;
  phone?: string;
}

// Cloud Identity Info DTO (matching .NET CloudIdentityInfoDto)
export interface CloudIdentityInfoDto {
  customerType?: string;
  primaryDomain?: string;
  isDomainVerified?: string;
  alternateEmail?: string;
  phoneNumber?: string;
  languageCode?: string;
  adminConsoleUri?: any;
  eduData?: EduDataDto;
}

// Edu Data DTO (matching .NET EduDataDto)
export interface EduDataDto {
  instituteType?: string;
  instituteSize?: string;
  website?: string;
}

// Place Transition Request (matching .NET PlaceTransitionRequest)
export interface PlaceTransitionRequest {
  BrandId?: string;
  StoreId?: string;
  PartnerId?: string;
  CustomerId?: string;
  FromSubscriptionId?: string;
  FromMaterialId?: string;
  FromCatalogItemId?: string;
  Quantity?: number;
  ToSubscriptionId?: string;
  ToCatalogItemId?: string;
  PromotionId?: string;
  Term?: string;
  BillFreq?: string;
  UpdateType?: string;
  SubUpdateType?: string;
  UnitPrice?: number;
  TaxRate?: number;
  PromotionRate?: number;
  Segment?: string;
}

// Get Customer Tenant ID and MPN ID Response (matching .NET GetCustomerTenantIdAndMPNIdResponse)
export interface GetCustomerTenantIdAndMPNIdResponse {
  CustomerTenantId?: string;
  MPNId?: string;
}

// Azure Unbilled Usage DTO
export type AzureUnbilledUsageDto = {
  storeId: string;
  brandId: string;
  currencyCode: string;
  billingPeriod: string;
  attributeSet: string;
};

// Azure Unbilled Usage Request (for service layer)
export type AzureUnbilledUsageRequest = {
  currencyCode: string;
  billingPeriod: string;
  attributeSet: string;
};

// Azure Entitlement Item
export type AzureEntitlementItem = {
  id: string;
  friendlyName: string;
  status: string;
  subscriptionId: string;
};

// Customer Azure Entitlement Response
export interface AzureEntitlementResponse {
  totalCount: number;
  items: AzureEntitlementItem[];
}

// Subscription Entitlement
export interface SubscriptionEntitlement {
  subscriptionId: string;
  customerId: string;
  entitlementId: string;
  friendlyName: string;
  status: string;
}

// Entitlement Model
export interface EntitlementModel {
  totalCount: number;
  entitlements: SubscriptionEntitlement[];
}

// Indirect Reseller Response Item
export interface IndirectResellerResponseItem {
  id: string;
  name: string;
  relationshipType: string;
  state: string;
  mpnId: string;
  location: string;
  errorMessage: string;
}

// Google API Response (matching .NET GoogleAPIResponse)
export interface GoogleAPIResponse {
  Message: string;
  Data?: any;
  StatusCode: number;
  IsError: boolean;
}

// Google Operation Response (matching .NET Operation class)
export interface Operation {
  Name?: string;
  OperationId?: string;
}

// Legacy interface for backward compatibility
export interface GoogleApiResponse {
  Message: string;
  Data?: any;
  StatusCode?: number;
  IsError: boolean;
}

export interface GoogleRenewalDetails {
  Quantities: {
    CurrentAssigned: number | null;
    TotalPurchased: number | null;
  };
}

export interface GoogleEntitlementExtension {
  Name: string;
  Parameters: {
    Name: string;
    Value: {
      Int64Value: number;
    };
  }[];
  GoogleCustomerId: string;
  EntitlementId: string;
}

export interface GoogleErrorResponse {
  Error: GoogleErrorResponseModel;
}

export interface GoogleErrorResponseModel {
  Code: number;
  Message: string;
  Status?: string;
  Details?: GoogleErrorDetails[];
}

export interface GoogleErrorDetails {
  Type?: string;
  ErrorMessages?: GoogleErrorMessages[];
  [key: string]: any;
}

export interface GoogleErrorMessages {
  ErrorMessage?: string;
  TranslatedErrorMessage?: string;
}

// Google Domain Verification types (matching .NET classes)
export interface CloudIdentityAccountDto {
  StoreId: string;
  Domain: string;
  IsExisting: boolean;
  ChannelPartnerId?: string;
}

// Camel case version for request body
export interface CloudIdentityAccountDtoRequest {
  storeId: string;
  domain: string;
  isExisting: boolean;
  channelPartnerId?: string;
}

export interface GoogleEndCustomerVerifyDomainResponse {
  Message: string;
  IsAvailable: boolean;
  IsLinkable: boolean;
  Data?: GoogleDomainExistsResponse;
  StatusCode: number;
}

export interface GoogleDomainExistsResponse {
  cloudIdentityAccounts: CloudIdentityAccount[];
}

export interface CloudIdentityAccount {
  Existing?: boolean;
  Owned?: boolean;
  CustomerName?: string;
  CustomerCloudIdentityId?: string;
}



// Google Customer Detail Model (matching .NET GoogleCustomerDetailModel)
export interface GoogleCustomerDetailModel {
  orgDisplayName?: string;
  orgPostalAddress?: CustOrgPostalAddress;
  primaryContactInfo?: CustPrimaryContactInfo;
  alternateEmail?: string;
  domain?: string;
  languageCode?: string;
  cloudIdentityInfo?: CustCloudIdentityInfo;
  channelPartnerId?: string;
  correlationId?: string;
  cloudIdentityId?: string;
  googleCustomerId?: string;
  name?: string;
  [key: string]: any; // Allow for additional dynamic properties from Google API
}

// Customer Cloud Identity Info (matching .NET CustCloudIdentityInfo)
export interface CustCloudIdentityInfo {
  customerType?: string;
  primaryDomain?: string;
  isDomainVerified?: string;
  alternateEmail?: string;
  phoneNumber?: string;
  languageCode?: string;
  adminConsoleUri?: any;
  eduData?: EduData;
}

// Education Data (matching .NET EduData)
export interface EduData {
  instituteType?: string;
  instituteSize?: string;
  website?: string;
}

// Customer Organization Postal Address (matching .NET CustOrgPostalAddress)
export interface CustOrgPostalAddress {
  revision?: number;
  regionCode?: string;
  languageCode?: string;
  postalCode?: string;
  sortingCode?: any;
  administrativeArea?: string;
  locality?: any;
  sublocality?: any;
  addressLines?: string[];
  recipients?: string[];
  organization?: string;
}

// Customer Primary Contact Info (matching .NET CustPrimaryContactInfo)
export interface CustPrimaryContactInfo {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  email?: string;
  title?: string;
  phone?: string;
}

// Google Validate End Customer DTO (matching .NET GoogleValidateEndCustomerDto)
export interface GoogleValidateEndCustomerDto {
  storeId: string;
  brandId: string;
  partnerId: string;
  productId: string;
  skuId: string;
  offerId: string;
  custId: string;
  cpId: string;
  googleCustomerId: string;
  cloudIdentityId: string;
}

// Google Validate End Customer Request (matching .NET GoogleValidateEndCustomerRequest)
export interface GoogleValidateEndCustomerRequest {
  storeId: string;
  brandId: string;
  partnerId: string;
  productId: string;
  skuId: string;
  offerId: string;
  custId: string;
  cpId: string;
  googleCustomerId: string;
  cloudIdentityId: string;
}

// CP ID Response Model (matching .NET CPIdResponseModel)
export interface CPIdResponseModel {
  Message?: string;
  Data?: CPIdData;
  StatusCode: number;
  IsError: boolean;
}

// CP ID Data (matching .NET CPIdData)
export interface CPIdData {
  CpiStatus?: string;
  InvitationLink?: string;
  PrimaryDomain?: string;
  Name?: string;
  PublicId?: string;
  IsDomainVerified?: boolean;
}

// Channel Partner Link State Response (matching .NET ChannelPartnerLinkStateResponse)
export interface ChannelPartnerLinkStateResponse {
  Name?: string;
  ResellerCloudIdentityId?: string;
  LinkState?: string;
  InviteLinkUri?: string;
  CreateTime?: string;
  UpdateTime?: string;
  PublicId?: string;
  channelPartnerCloudIdentityInfo?: ChannelPartnerCloudIdentityInfo;
}

// Channel Partner Cloud Identity Info (matching .NET ChannelPartnerCloudIdentityInfo)
export interface ChannelPartnerCloudIdentityInfo {
  IsDomainVerified?: boolean;
  PrimaryDomain?: string;
}

// Google Purchasable Offers Model (matching .NET GooglePurchasableOffersModel)
export interface GooglePurchasableOffersModel {
  purchasableOffers?: PurchasableOffer[];
  nextPageToken?: string;
}

// Purchasable Offer (matching .NET PurchasableOffer)
export interface PurchasableOffer {
  offer?: Offer;
  [key: string]: any;
}

// Offer (matching .NET Offer)
export interface Offer {
  name?: string;
  sku?: Sku;
  [key: string]: any;
}

// SKU (matching .NET Sku)
export interface Sku {
  [key: string]: any;
}

// Google Customer Details Model (matching .NET GoogleCustomerDetailsModel)
export interface GoogleCustomerDetailsModel {
  ChannelPartnerId?: string;
  CloudIdentityId?: string;
  Domain?: string;
  CustomerType?: string;
  CompanyName?: string;
  FirstName?: string;
  LastName?: string;
  Email?: string;
  AlternateEmail?: string;
  RegionCode?: string;
  AdministrativeArea?: string;
  Locality?: string;
  AddressLine1?: string;
  AddressLine2?: string;
  AddressLine3?: string;
  PostalCode?: string;
  InstituteType?: string;
  InstituteSize?: string;
  Website?: string;
  PhoneNumber?: string;
}

// Promotion Eligibility (matching .NET PromotionEligibility)
export interface PromotionEligibility {
  MaterialId?: string;
  Term?: string;
  BillType?: string;
  Segment?: string;
  Quantity?: number;
  IsEligible?: boolean | null;
  PromotionId?: string;
  CatalogItemId?: string;
  PromotionName?: string;
  PromotionDescription?: string;
  PolicyType?: string;
  PromotionValue?: number;
  Errors?: PromotionError[];
  StartDate?: Date | null;
  EndDate?: Date | null;
}

// Promotion Error (matching .NET Error)
export interface PromotionError {
  MinimumRequiredSeats?: number;
  MaximumRequiredSeats?: number;
  AvailableSeats?: number;
  Type?: string;
  Description?: string;
}

// Renewal Promotion Detail DTO (matching .NET RenewalPromotionDetailDto)
export interface RenewalPromotionDetailDto {
  promotionEligibilities: PromotionEligibilityDto[];
}

// Promotion Eligibility DTO (matching .NET PromotionEligibilityDto)
export interface PromotionEligibilityDto {
  MaterialId?: string;
  Term?: string;
  BillType?: string;
  Segment?: string;
  Quantity?: number;
  IsEligible?: boolean | null;
  PromotionId?: string;
  CatalogItemId?: string;
  PromotionName?: string;
  PromotionDescription?: string;
  PolicyType?: string;
  PromotionValue?: number;
  Errors?: PromotionError[];
  StartDate?: Date | null;
  EndDate?: Date | null;
}

// Search Cisco Customer DTO (matching .NET SearchCiscoCustomerDto)
export interface SearchCiscoCustomerDto {
  countryCode: string;
  name: string;
  storeId: string;
  pageSize: number;
}

// Cisco Customer Response (matching .NET CiscoCustomerResponse)
export interface CiscoCustomerResponse {
  bodID?: string;
  status?: string;
  matchFound?: number;
  party?: CiscoGetAllCustomerPartyResponse[];
  errorCode?: string;
  message?: string;
}

// Cisco Get All Customer Party Response (matching .NET CiscoGetAllCustomerPartyResponse)
export interface CiscoGetAllCustomerPartyResponse {
  partyName?: string;
  localeCode?: string;
}

// Cisco Customer Request (matching .NET CiscoCustomerRequest)
export interface CiscoCustomerRequest {
  bodID?: string;
  get?: CiscoCustomerPageSize;
  party?: CiscoCustomerParty;
  timestamp?: string;
  storeId?: string;
}

// Cisco Customer Page Size (matching .NET CiscoCustomerPageSize)
export interface CiscoCustomerPageSize {
  pageSize: number;
}

// Cisco Customer Party (matching .NET CiscoCustomerParty)
export interface CiscoCustomerParty {
  countryCode?: string;
  name?: string;
}

// Cisco Customer Validate DTO (matching .NET CiscoCustomerValidateDto)
export interface CiscoCustomerValidateDto {
  custId: string;
  storeId: string;
  partyName: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  countryCode: string;
  businessContactName: string;
  businessContactEmail: string;
  businessContactNumber: string;
}

// Cisco Status Response (matching .NET StatusResponse)
export interface CiscoStatusResponse {
  Status?: string;
  Message?: string;
}

// Cisco Status Response Type (matching .NET StatusResponseType)
export interface CiscoStatusResponseType extends CiscoStatusResponse {
  ActionType?: string;
}

// Cisco Validate Customer Request (matching .NET CiscoValidateCustomerRequest)
export interface CiscoValidateCustomerRequest {
  storeId?: string;
  bodID?: number;
  timestamp?: string;
  party?: CiscoCustomerValidateParty[];
}

// Cisco Customer Validate Party (matching .NET CiscoCustomerValidateParty)
export interface CiscoCustomerValidateParty {
  sequenceID?: number;
  partyName?: string;
  postalCode?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  countryCode?: string;
  county?: string;
  state?: string;
}

// Cisco Customer Model (matching .NET CiscoCustomerModel)
export interface CiscoCustomerModel {
  custId?: string;
  storeId?: string;
  partyName?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  countryCode?: string;
  businessContactName?: string;
  businessContactEmail?: string;
  businessContactNumber?: string;
}

// Cisco Validate Customer Response (matching .NET CiscoValidateCustomerResponse)
export interface CiscoValidateCustomerResponse {
  status?: string;
  message?: string;
  party?: CiscoValidateCustomerPartyResponse[];
}

// Cisco Validate Customer Party Response
export interface CiscoValidateCustomerPartyResponse {
  geoValidation?: CiscoValidationResult;
  nameValidation?: CiscoValidationResult;
}

// Cisco Validation Result
export interface CiscoValidationResult {
  status?: string;
  message?: string;
}

// Subscription Response Model (matching .NET SubscriptionResponseModel)
export interface SubscriptionResponseModel {
  OrderId?: string;
  SubscriptionId?: string;
  MaterialId?: string;
  MaterialNo?: string;
  MaterialDesc?: string;
  Qty?: number;
  MaterialType?: string;
  PlanType?: string;
  StartDate?: string;
  EndDate?: string;
  AddAtt1?: string; // Custid
  AddAtt2?: string; // CustName
  EndCustomerTenantID?: string;
  AddAtt3?: string; // BrandName
  AddAtt4?: string; // ContractId
  AddAtt5?: string; // Status
  AddAtt6?: string; // PSubscriptionId
  PartnerId?: string;
  CompanyName?: string;
  IsSubscriptionRegistered?: boolean;
  Segment?: string;
  Term?: string;
  SubscriptionEntitlements?: SubscriptionEntitlement[];
  CiscoAdditionalInformation?: CiscoAdditionalInformation;
}

// Subscription Entitlement
export interface SubscriptionEntitlement {
  SubscriptionId?: string;
  CustomerId?: string;
  EntitlementId?: string;
  FriendlyName?: string;
  Status?: string;
}

// Cisco Additional Information (matching .NET CiscoAdditionalInformation)
export interface CiscoAdditionalInformation {
  CiscoOrderDetails?: CiscoOrderDetail[];
  ProvisioningAttributes?: CiscoProvisioningAttributes;
}

// Cisco Order Detail (matching .NET CiscoOrderDetail)
export interface CiscoOrderDetail {
  OrderKey?: number; // JsonIgnore in .NET but included for internal use
  MaterialNo?: string;
  MaterialDesc?: string;
  MaterialType?: string;
  Qty?: number;
}

// Cisco Provisioning Attributes (matching .NET CiscoProvisioningAttributes)
export interface CiscoProvisioningAttributes {
  SubscriptionId?: string; // JsonIgnore in .NET but included for internal use
  OrderKey?: number; // JsonIgnore in .NET but included for internal use
  FirstName?: string;
  LastName?: string;
  CompanyName?: string;
  EmailAddress?: string;
  PhoneNumber?: string;
  DomainName?: string;
  CountryCode?: string;
  TimeZone?: string;
  DataCenterRegion?: string;
  BusinessId?: string;
}

// Entitlements Response (for DAL response)
export interface EntitlementsResponse {
  TotalCount: number;
  Entitlements: SubscriptionEntitlement[];
}

// Cisco Additional Information Response (for DAL response)
export interface CiscoAdditionalInformationResponse {
  ProvisioningAttributes: CiscoProvisioningAttributes[];
  CiscoOrderDetails: CiscoOrderDetail[];
}

// Terminate Order types (matching .NET TerminateOrder classes)
export interface GetSubscriptionRequest {
  Brand?: string;
  SubscriptionId?: string; // DefaultAzureSubscriptionId used for Azure RI
  OrderId?: string;
  CatalogId?: string;
  GrantType?: string;
  UserName?: string;
  Password?: string;
  ClientId?: string;
  Resource?: string;
  Clientsecret?: string;
  StoreDomain?: string;
  Token?: string;
  CustomerId?: string;
  Qty?: number;
}

export interface TerminateLineItems {
  LineItemNumber?: number;
  OfferId?: string;
}

export interface TerminateOrder {
  Id?: string;
  Status?: string;
  LineItems?: TerminateLineItems[];
}

export interface SubscriptionStatusResponse {
  Id?: string;
  SubscriptionId?: string;
  Status?: string;
  ErrorMessage?: string;
}

// Cancellation Status (matching .NET CancellationStatus)
export interface CancellationStatus {
  Status?: string;
  Message?: string;
}

// Subscription Object (matching .NET SubscriptionObj)
export interface SubscriptionObj {
  SubscriptionId?: string;
}

// Patch Subscription Model (using camelCase to match request body)
export interface PatchSubscriptionModel {
  storeId?: string;
  customerId?: string;
  subscriptionId?: string;
  status?: string;
  quantity?: number;
  requestId?: string;
  autoRenewEnabled?: boolean;
  actualQty?: number;
  updateType?: string;
  partnerId?: string;
  autoRenewToUpdate?: boolean | null;
}

// Get Subscriptions By Customer Id And Material Id Response (matching .NET GetSubscriptionsByCustomerIdAndMaterialIdResponse)
export interface GetSubscriptionsByCustomerIdAndMaterialIdResponse {
  SubscriptionId?: string;
  MaterialId?: string;
  MaterialDesc?: string;
  Term?: string;
  BillType?: string;
}

// NCE Product Upgrades Response Types (matching .NET classes)

// Get NCE Product Upgrades Response (matching .NET GetNCEProductUpgradesResponse)
export interface GetNCEProductUpgradesResponse {
  Status?: boolean;
  Message?: string;
  Items?: GetNCEProductUpgradesResponseItem[];
}

// Get NCE Product Upgrades Response Item (matching .NET GetNCEProductUpgradesResponseItem)
export interface GetNCEProductUpgradesResponseItem {
  MaterialId?: string;
  MaterialDesc?: string;
  CatalogItemId?: string;
  Terms?: string[];
  BillTypes?: string[];
}

// Eligible Product (matching .NET EligibleProduct)
export interface EligibleProduct {
  CatalogItemId?: string;
  MaterialId?: string;
  Description?: string;
}

// Validate Get NCE Product Upgrades Response (matching .NET ValidateGetNCEProductUpgradesResponse)
export interface ValidateGetNCEProductUpgradesResponse {
  IsSuccess?: boolean;
  CustomerId?: string;
  ErrorMessage?: string;
}

// Microsoft NCE Product Upgrades Response Types (matching .NET Microsoft response classes)

// Get NCE Product Upgrades Microsoft Response (matching .NET GetNCEProductUpgradesMicrosoftResponse)
export interface GetNCEProductUpgradesMicrosoftResponse {
  TotalCount?: number;
  Items?: GetNCEProductUpgradesMicrosoftProductItem[];
}

// Get NCE Product Upgrades Microsoft Product Item (matching .NET GetNCEProductUpgradesMicrosoftProductItem)
export interface GetNCEProductUpgradesMicrosoftProductItem {
  CatalogItemId?: string;
  Title?: string;
  Description?: string;
  Quantity?: number;
  Eligibilities?: GetNCEProductUpgradesMicrosoftProductItemEligibility[];
}

// Get NCE Product Upgrades Microsoft Product Item Eligibility (matching .NET GetNCEProductUpgradesMicrosoftProductItemEligibility)
export interface GetNCEProductUpgradesMicrosoftProductItemEligibility {
  IsEligible?: boolean;
  TransitionType?: string;
  Errors?: ResponseError[];
}



// SAP Invoice Document types (matching .NET classes)
export interface MagentoResponse {
  Status: string;
  Message: string;
  Content?: string;
}

export interface GetSAPInvoiceDocumentDto {
  InvoiceNo: string;
}

export interface GetSAPInvoiceDocumentModel {
  InvoiceNo: string;
}

export interface SapPreInvoiceResponseModel {
  PreInvoiceNo: string;
  InvoiceNo: string;
  InvoiceDate: string;
  SalesOrderNo: string;
  InvoiceDoc: string;
  InvoiceValues: InvoiceValuesModel[];
  PaymentTermCode: string;
  PaymentTermDescription: string;
  DueDate: string;
  StoreId: string;
}

export interface InvoiceValuesModel {
  Currency: string;
  TotalPrice: number;
  Tax: TaxModel[];
}

export interface TaxModel {
  PreInvoiceNo: string;
  TaxCode: string;
  TaxPercentage: number;
  TaxValue: number;
}

export interface S3Credentials {
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  AWS_REGION: string;
  AWS_BUCKETNAME: string;
}

// Renewal Request MS DTO (matching .NET RenewalRequestMSDto)
export interface RenewalRequestMSDto {
  Skey: number;
  BrandId: string;
  StoreId: string;
}

// AWS Payer Account Request (matching .NET AwsPayerAccountRequest)
export interface AwsPayerAccountRequest {
  PayerAccountId: string;
  RequestId: string;
  Month: number;
  Year: number;
}

// Pull CSV Invoices Background Request DTO
export interface PullCsvInvoicesBackgroundDto {
  requestId: string;
  username: string;
  month: number;
  year: number;
  storeKeyInput: string;
}

// Eligibility Response (matching .NET EligibilityResponse)
export interface EligibilityResponse {
  TotalCount: number;
  Items: PromotionItem[];
}

// Promotion Item (matching .NET PromotionItem)
export interface PromotionItem {
  Id: number;
  CatalogItemId: string;
  Quantity: number;
  BillingCycle: string;
  TermDuration: string;
  Eligibilities: Eligibility[];
}

// Eligibility (matching .NET Eligibility)
export interface Eligibility {
  PromotionId: string;
  IsEligible: boolean;
  Errors: PromotionEligibilityError[];
}

// Promotion Eligibility Error (matching .NET Error)
export interface PromotionEligibilityError {
  MinimumRequiredSeats: number;
  MaximumRequiredSeats: number;
  AvailableSeats: number;
  Type: string;
  Description: string;
}

// Promotion Model (matching .NET PromotionModel)
export interface PromotionModel {
  StoreId: string;
  CustomerId: string;
  PromotionEligibility: PromotionEligibility[];
}

// Base Promotions (matching .NET BasePromotions)
export interface BasePromotions {
  PromotionId: string;
  PromotionName: string;
  PolicyType: string;
  PromotionValue: number;
  Term: string;
  BillType: string;
  StartDate: Date;
  EndDate: Date;
  MaterialId: string;
  Description: string;
}

// Promotions (matching .NET Promotions)
export interface Promotions extends BasePromotions {
  PromotionDescription: string;
  Segment: string;
}

// SKU Response (matching .NET SkuResponse)
export interface SkuResponse {
  items: SkuItem[];
}

// SKU Item (matching .NET SkuItem)
export interface SkuItem {
  catalogItemId: string;
  segment: string;
}

// Subscription Model (matching .NET SubscriptionModel)
export interface SubscriptionModel {
  SubscriptionId: string;
  EndDate: Date;
  CustomerId: string;
  BrandId: string;
  StoreId: string;
  StoreKey: number;
  PlanType: string;
  Duration: string;
  MaterialId: string;
  MaterialDescription: string;
  Quantity: number;
  Term: string;
  Status: string;
}

// Subscription Promotion Detail Model (matching .NET SubscriptionPromotionDetailModel)
export interface SubscriptionPromotionDetailModel {
  PromotionId?: string;
  PromotionName?: string;
  PromotionDescription?: string;
  PromotionEndDate?: Date;
  PromotionStartDate?: Date;
  SubscriptionEndDate?: Date;
  Segment?: string;
  BillType?: string;
  Term?: string;
  Discount?: number;
  promotionEligibilities: PromotionEligibility[];
}

// AWS Credentials
export interface AWSCredentials {
  Id: number;
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  AWS_REGION: string;
  RESOURCE: string;
  AWS_BUCKETNAME: string;
  APPLICATION: string;
  COMMENTS: string;
  ACTIVE?: boolean;
}
