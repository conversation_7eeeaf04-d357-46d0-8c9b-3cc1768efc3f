import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { getMethod } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import logger from "../../utils/logger";
import { CustomerValidationStatusResponse } from "../../types/responses/customResponse";

export async function getCustomerValidationStatusService(
  req: MedusaRequest,
  customerId: string,
  storeId: string
): Promise<CustomerValidationStatusResponse> {
  const errors: string[] = [];
  logger.info(`Going to Call Get Customer Validation Status Service`);
  if (!customerId) errors.push("customerId is required");
  if (!storeId) errors.push("storeId is required");

  if (errors.length > 0) {
    return {
      status: false,
      message: errors.join(", "),
    };
  }
  const configModule = req.scope.resolve("configModule");

  const storeData = await getStoreDetails({ storeId });

  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "",
    redirect_uri: storeData.redirecturi || "",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  const headerList = {
    Authorization: `Bearer ${token.access_token}`,
  };

  const completeUrlWithParams =
    MicrosoftEndpoints.getCustomerValidationStatusUrl(customerId);

  const response = await getMethod(
    {
      url: completeUrlWithParams,
      headers: headerList,
      isVendorHit: true,
      module: "getCustomerValidationStatus",
    },
    configModule
  );

  if (response.isSuccessStatusCode) {
    const result = JSON.parse(response.content || "{}");
    const status = result.status || "";
    const lastUpdate = result.lastUpdateDateTime || "N/A";

    switch (status) {
      case "UnderReview":
      case "Unknown":
        return {
          status: false,
          message: `Status is ${status}. Your customer’s tenant is being validated by Microsoft; it may take 24-72 hours to complete. Please try again later. Last Update Date/Time : ${lastUpdate}.`,
        };
      case "NotAllowed":
        return {
          status: false,
          message: `Status is ${status}. Microsoft has restricted purchase in this customer tenant. Please reach out to your account manager for more information. Last Update Date/Time : ${lastUpdate}.`,
        };
      case "":
        return {
          status: true,
          message: `Status is (Empty String). Last Update Date/Time : ${lastUpdate}.`,
        };
      default:
        return {
          status: true,
          message: `Status is ${status}. Last Update Date/Time : ${lastUpdate}.`,
        };
    }
  } else if (response.httpStatusCode === 403) {
    const error = JSON.parse(response.content || "{}");
    console.log("error------------->",error);
    return {
      status: false,
      message: error.message || "Resource not found",
    };
  } else if (response.httpStatusCode === 404){
    const error = JSON.parse(response.content || "{}");
    return {
      status: false,
      message: error.message || "Resource not found",
    };
  } else {
    const error = JSON.parse(response.content || "{}");
    return {
      status: false,
      message: error.message || response.errorMessage || "Unknown error",
    };
  }
}
