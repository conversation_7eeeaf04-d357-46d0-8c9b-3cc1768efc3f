import AWS from 'aws-sdk';
import logger from "../../utils/logger";
import { AWSCredentials } from "../../types/responses/customResponse";
import { getAWSCredentials, validateAWSCredentials } from "../../config/awsCredentialsConfig";

/**
 * Get Pre-Signed URL for S3 Object
 * @param awsCred - AWS credentials object containing access keys, region, and bucket info
 * @param keyName - S3 object key/filename to generate pre-signed URL for
 * @param preSignedUrlRetentionHours - Number of hours the pre-signed URL should remain valid
 * @returns Promise<string> - Pre-signed URL or empty string if object not found
 */
export async function getPreSignedUrlService(
  awsCred: AWSCredentials,
  keyName: string,
  preSignedUrlRetentionHours: number
): Promise<string> {
  logger.info("Entered into GetPreSignedUrl method.");

  // Handle SSL certificate issues - disable SSL verification for development
  const originalRejectUnauthorized = process.env.NODE_TLS_REJECT_UNAUTHORIZED;

  // Always disable SSL verification
  logger.warn("Disabling SSL verification");
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

  try {
    // Create S3 client
    const s3Client = new AWS.S3({
      accessKeyId: awsCred.AWS_ACCESS_KEY_ID,
      secretAccessKey: awsCred.AWS_SECRET_ACCESS_KEY,
      region: awsCred.AWS_REGION,
    });

    // Check if object exists first
    await s3Client.headObject({
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
    }).promise();

    // Generate pre-signed URL
    const params = {
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
      Expires: preSignedUrlRetentionHours * 3600, // Convert hours to seconds
    };

    const preSignedUrl = await s3Client.getSignedUrlPromise('getObject', params);

    logger.info(`Pre-signed URL generated successfully for key: ${keyName}`);
    return preSignedUrl;

  } catch (error: any) {
    // Handle S3 exceptions
    logger.error(`Error occurred in GetPreSignedUrl method. Error: ${error.message}, stackTrace: ${error.stack}`);

    // Check for NotFound status
    if (error.statusCode === 404) {
      return ""; // Return empty string
    }

    // Re-throw other exceptions
    throw error;

  } finally {
    // Restore original SSL setting
    if (originalRejectUnauthorized !== undefined) {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = originalRejectUnauthorized;
    } else {
      delete process.env.NODE_TLS_REJECT_UNAUTHORIZED;
    }
  }
}


/**
 * Get Pre-Signed URL using credentials from configuration
 * Convenience method that retrieves AWS credentials automatically
 */
export async function getPreSignedUrlWithConfig(
  resource: string,
  keyName: string,
  preSignedUrlRetentionHours: number
): Promise<string> {
  logger.info(`Getting pre-signed URL with config for resource: ${resource}, key: ${keyName}`);

  // Get AWS credentials from configuration
  const awsCredentials = await getAWSCredentials(resource);

  if (!awsCredentials) {
    const errorMessage = `AWS credentials not found for resource: ${resource}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Validate credentials
  if (!validateAWSCredentials(awsCredentials)) {
    const errorMessage = `Invalid AWS credentials for resource: ${resource}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Call the main service with retrieved credentials
  return await getPreSignedUrlService(awsCredentials, keyName, preSignedUrlRetentionHours);
}