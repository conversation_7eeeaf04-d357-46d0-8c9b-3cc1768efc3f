import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import {
  createMicrosoftRequestHeaders,
} from "../../config/microsoftEndpoints";
import { AzureUnbilledUsageRequestType } from "../../validators/azure/azureUnbilledUsageValidator";
import { Urls } from "../../utils/constants";

export type MicrosoftApiResponse = {
  Message: string;
  Data?: any;
  StatusCode: number;
  IsError: boolean;
};

export type AzureServiceResponse = {
  response: MicrosoftApiResponse;
  headers?: Record<string, string>;
};
export async function exportUnbilledUsageService(
  req: MedusaRequest,
  storeId: string,
  brandId: string,
  request: AzureUnbilledUsageRequestType
): Promise<AzureServiceResponse> {
  const apiResponse: MicrosoftApiResponse = {
    Message: "Success",
    StatusCode: 200,
    IsError: false,
  };

  try {
    logger.info(`Going to hit BL GetStoreDetails method with ${JSON.stringify(request)}`);
    
    // Get store details
    const storeDetails = await getStoreDetails({ storeId, brandId, credentialType:'GRAPH' });

    if (!storeDetails) {
      logger.error(`Store details not found for storeId=${storeId}, brandId=${brandId}`);
      return {
        response: {
          Message: "Store details not found",
          StatusCode: 400,
          IsError: true,
        },
      };
    }

    // Generate Microsoft token
    const token = await getMsToken({
      brand: storeDetails.brand || "",
      client_id: storeDetails.clientid || "",
      client_secret: storeDetails.clientsecret || "",
      grant_type: storeDetails.granttype || "",
      markValue: storeDetails.markvalue?.toString() || "",
      redirect_uri: storeDetails.redirecturi || "",
      refresh_token: storeDetails.token || "",
      resource: storeDetails.resource || "",
      store_domain: storeDetails.storedomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`Token generation failed`);
      return {
        response: {
          Message: "Error While Generating Token",
          StatusCode: 500,
          IsError: true,
        },
      };
    }

    // Build export URL
    const baseUrl = Urls.GRAPH_BASE_URL || "";
    const exportUrl = `${baseUrl}${Urls.MS_EXPORT_AZURE_UNBILLED_USAGE_URL}`;

    // Create Microsoft request headers
    const headerList = createMicrosoftRequestHeaders(
      token.access_token,
      storeId
    );

    logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${exportUrl}, RequestBody-${JSON.stringify(request)}, headerlist-${JSON.stringify(headerList)}`);

    const configModule = req.scope.resolve("configModule");
    
    // Call Microsoft API
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: exportUrl,
        body: request,
        headers: headerList,
        isVendorHit: true,
        module: "ExportAzureBilledUsage",
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      // Success response
      apiResponse.StatusCode = response.httpStatusCode;
      
      // Parse response content if available
      if (response.content) {
        try {
          apiResponse.Data = JSON.parse(response.content);
        } catch {
          apiResponse.Data = response.content;
        }
      }
    } else {
      // Error response
      logger.info(`Received errored response and deserialising response.ErrorMessage-${response.errorMessage}`);
      
      let errorMessage = "Unknown error";
      try {
        // Try to parse Microsoft error response
        const errorResponse = JSON.parse(response.content || response.errorMessage || "{}");
        errorMessage = errorResponse?.error?.message || errorResponse?.message || response.errorMessage || "Unknown error";
      } catch {
        errorMessage = response.errorMessage || "Unknown error";
      }

      apiResponse.Message = errorMessage;
      apiResponse.StatusCode = response.httpStatusCode;
      apiResponse.IsError = true;
    }
    // Create empty headers object for now
    const responseHeaders: Record<string, string> = {};
    
    logger.info(`Going to return response from BL. ApiResponse-${JSON.stringify(apiResponse)}, Headers-${JSON.stringify(responseHeaders)}`);
    
    return {
      response: apiResponse,
      headers: responseHeaders,
    };
  } catch (error) {
    logger.error(`ExportUnbilledUsage Service Error:`, error);
    
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    
    return {
      response: {
        Message: `Exception: ${errorMessage}`,
        StatusCode: 422, // UnprocessableEntity
        IsError: true,
      },
    };
  }
}
