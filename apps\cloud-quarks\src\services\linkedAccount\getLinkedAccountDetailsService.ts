import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";

export interface LinkedAccountIdDetailModel {
  PartnerName: string;
  SapCompany: string;
  EmailId: string;
  StoreID: string;
  CustName: string;
  SubscriptionId: string;
  CustEmail: string;
  SapCustId: string;
  PartnerId: string;
  CustId: string;
}

export async function getLinkedAccountDetailsService(
  linkedAccountId: string
): Promise<LinkedAccountIdDetailModel> {
  const pool = await getSqlServerConnection();
  const request = pool.request();
  request.input("ilinkedAccountId", linkedAccountId);

  logger.info(
    `Calling [usp_sel_linked_account_details] with linkedAccountId=${linkedAccountId}`
  );

  const result = await request.execute("usp_sel_linked_account_details");
  const record = result.recordset?.[0];

  logger.info(`Linked account detail: ${JSON.stringify(record)}`);

  if (!record) {
    throw new Error(`No linked account found for ID: ${linkedAccountId}`);
  }

  // Map database result to interface format
  const mappedResult: LinkedAccountIdDetailModel = {
    PartnerName: record.partnername || "",
    SapCompany: record.sapcompany || "",
    EmailId: record.emailid || "",
    StoreID: record.storeid || "",
    CustName: record.custname || "",
    SubscriptionId: Array.isArray(record.subscriptionid)
      ? record.subscriptionid.join(",")
      : (record.subscriptionid || ""),
    CustEmail: record.CustEmail || "",
    SapCustId: record.sapcustid || "",
    PartnerId: record.partnerId || "",
    CustId: record.custId || "",
  };

  logger.info(`Mapped linked account detail: ${JSON.stringify(mappedResult)}`);

  return mappedResult;
}
