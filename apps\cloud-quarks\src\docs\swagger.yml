openapi: 3.0.1
info:
  title: CloudQuarks Service Provider
  version: 1.0.0
  description: CloudQuarks - Business validations API,s
servers:
  - url: http://localhost:8086
    description: Local development server
paths:
  /v1/authentication/getDocuSignAccessToken:
    get:
      summary: Get DocuSign access token using JWT authentication
      tags:
        - Authentication
      responses:
        '200':
          description: DocuSign access token retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  access_token:
                    type: string
                    example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
                  token_type:
                    type: string
                    example: Bearer
                  expires_in:
                    type: number
                    example: 3600
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Internal server error
  /v1/AwsS3Bucket/pullCsvInvoicesInBackground:
    get:
      summary: Pull CSV invoices in background
      tags:
        - AWS S3 Bucket
      parameters:
        - in: query
          name: requestId
          required: true
          schema:
            type: string
          description: Request ID for the background process
          example: req-123456
        - in: query
          name: username
          required: true
          schema:
            type: string
          description: Username for the process
          example: <EMAIL>
        - in: query
          name: month
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 12
          description: Month for invoice processing
          example: 12
        - in: query
          name: year
          required: true
          schema:
            type: integer
            minimum: 2000
            maximum: 2100
          description: Year for invoice processing
          example: 2024
        - in: query
          name: storeKeyInput
          required: true
          schema:
            type: string
          description: Store key input for processing
          example: store-key-123
      responses:
        '200':
          description: Background process initiated successfully
          content:
            text/plain:
              schema:
                type: string
                example: SUBMITTED
        '400':
          description: Validation error
          content:
            text/plain:
              schema:
                type: string
                example: FAILED
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
                example: FAILED
  /v1/azure/exportBilledUsage:
    post:
      summary: Export Azure billed usage data
      tags:
        - Azure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - brandId
                - invoiceId
                - attributeSet
              properties:
                storeId:
                  type: string
                  example: store-123
                  description: Store ID
                brandId:
                  type: string
                  example: brand-456
                  description: Brand ID
                invoiceId:
                  type: string
                  example: invoice-789
                  description: Invoice ID for billed usage export
                attributeSet:
                  type: string
                  example: usage-attributes
                  description: Attribute set for usage data
            example:
              storeId: string
              brandId: string
              invoiceId: string
              attributeSet: string
      responses:
        '200':
          description: Billed usage data exported successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Success
                  data:
                    type: object
                    description: Billed usage data
                  statusCode:
                    type: number
                    example: 200
                  isError:
                    type: boolean
                    example: false
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 400
                  isError:
                    type: boolean
                    example: true
        '422':
          description: Unprocessable entity
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 422
                  isError:
                    type: boolean
                    example: true
  /v1/azure/exportUnbilledUsage:
    post:
      summary: Export Azure unbilled usage data
      tags:
        - Azure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - brandId
                - currencyCode
                - billingPeriod
                - attributeSet
              properties:
                storeId:
                  type: string
                  example: store-123
                  description: Store ID
                brandId:
                  type: string
                  example: brand-456
                  description: Brand ID
                currencyCode:
                  type: string
                  example: USD
                  description: Currency code for unbilled usage
                billingPeriod:
                  type: string
                  example: 2024-01
                  description: Billing period for usage data
                attributeSet:
                  type: string
                  example: usage-attributes
                  description: Attribute set for usage data
            example:
              storeId: string
              brandId: string
              currencyCode: string
              billingPeriod: string
              attributeSet: string
      responses:
        '200':
          description: Unbilled usage data exported successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Success
                  data:
                    type: object
                    description: Unbilled usage data
                  statusCode:
                    type: number
                    example: 200
                  isError:
                    type: boolean
                    example: false
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 400
                  isError:
                    type: boolean
                    example: true
        '422':
          description: Unprocessable entity
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 422
                  isError:
                    type: boolean
                    example: true
  /v1/azure/getAzureOperationDetails:
    get:
      summary: Get Azure operation details
      description: >
        Retrieves details for a specific Azure operation using the operation ID.

        This endpoint calls the Microsoft Partner Center API to get operation
        status and details.
      tags:
        - Azure
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store ID for authentication and context
          example: AE-EN
        - in: query
          name: brandId
          required: true
          schema:
            type: string
          description: Brand ID for authentication and context
          example: '943'
        - in: query
          name: operationId
          required: true
          schema:
            type: string
            format: uuid
          description: Azure operation ID (UUID format) to get details for
          example: f6fdbbd2-83c5-4925-ae3b-50db13d4bbdf
      responses:
        '200':
          description: Operation details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Success
                    description: Response message indicating success or error details
                  data:
                    type: object
                    description: Azure operation details data from Microsoft API
                    properties:
                      id:
                        type: string
                        example: f6fdbbd2-83c5-4925-ae3b-50db13d4bbdf
                        description: Operation ID
                      status:
                        type: string
                        example: completed
                        description: >-
                          Operation status (e.g., completed, failed,
                          in-progress)
                      createdDateTime:
                        type: string
                        format: date-time
                        example: '2024-07-29T10:30:00Z'
                        description: When the operation was created
                      lastActionDateTime:
                        type: string
                        format: date-time
                        example: '2024-07-29T10:35:00Z'
                        description: When the operation was last updated
                  statusCode:
                    type: number
                    example: 200
                    description: HTTP status code
                  isError:
                    type: boolean
                    example: false
                    description: Indicates if the response contains an error
        '400':
          description: Bad Request - Validation error or missing required parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: >-
                      StoreId can't be null or empty, BrandId can't be null or
                      empty
                    description: Detailed validation error message
                  statusCode:
                    type: number
                    example: 400
                    description: HTTP status code
                  isError:
                    type: boolean
                    example: true
                    description: Always true for error responses
        '422':
          description: Unprocessable Entity - Unexpected error or exception occurred
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Exception: Network timeout occurred'
                    description: Exception message with details about the error
                  statusCode:
                    type: number
                    example: 422
                    description: HTTP status code
                  isError:
                    type: boolean
                    example: true
                    description: Always true for error responses
        '500':
          description: >-
            Internal Server Error - Token generation failed or store details not
            found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Error While Generating Token
                    description: Error message describing the internal server error
                  statusCode:
                    type: number
                    example: 500
                    description: HTTP status code
                  isError:
                    type: boolean
                    example: true
                    description: Always true for error responses
  /v1/azure/retrieveAzureUsageOrCreateQueue:
    post:
      summary: Retrieve Azure usage data or create queue for processing
      tags:
        - Azure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - subscriptionId
                - customerId
                - period
              properties:
                storeId:
                  type: string
                  example: store-123
                  description: Store ID
                subscriptionId:
                  type: string
                  example: sub-456
                  description: Azure subscription ID
                customerId:
                  type: string
                  example: cust-789
                  description: Customer ID
                period:
                  type: string
                  example: 2024-01
                  description: Period for usage data
            example:
              storeId: string
              subscriptionId: string
              customerId: string
              period: string
      responses:
        '200':
          description: Azure usage data retrieved or queue created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Success
                  data:
                    type: object
                    description: Usage data or queue information
                  statusCode:
                    type: number
                    example: 200
                  isError:
                    type: boolean
                    example: false
                  type:
                    type: string
                    description: Response type indicator
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 400
                  isError:
                    type: boolean
                    example: true
        '422':
          description: Unprocessable entity
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 422
                  isError:
                    type: boolean
                    example: true
  /v1/contracts/getContract:
    get:
      summary: Get contract details by storeId and partnerId
      tags:
        - Contracts
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store ID
        - in: query
          name: partnerId
          required: true
          schema:
            type: string
          description: Partner ID
        - in: query
          name: expiryInDays
          required: false
          schema:
            type: string
            default: ALL
          description: Expiry filter (number of days or 'ALL')
      responses:
        '200':
          description: List of contracts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetContractResponse'
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /v1/contracts/getLastContractInfo:
    get:
      summary: Get last contract information by subscription ID
      tags:
        - Contracts
      parameters:
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
          description: Unique Subscription ID (e.g., S0BvabJVs3lBNl)
      responses:
        '200':
          description: Last contract information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      qty:
                        type: integer
                        example: 6
                      discount_percentage:
                        type: number
                        format: float
                        example: 0
                      discounted_unit_price:
                        type: number
                        format: float
                        example: 553
                      sub_total:
                        type: number
                        format: float
                        example: 3318
                      vat_percentage:
                        type: number
                        format: float
                        example: 5
                      promotion_percentage:
                        type: number
                        format: float
                        example: 0
                      net_price:
                        type: number
                        format: float
                        example: 3483.9
                      unit_price_after_promotion:
                        type: number
                        format: float
                        example: 553
                      unit_price_before_promotion:
                        type: number
                        format: float
                        example: 553
                      subscription_id:
                        type: string
                        example: string
                      promotion_id:
                        type: string
                        nullable: true
                        example: null
                      promotion_start_date:
                        type: string
                        format: date-time
                        nullable: true
                        example: null
                      promotion_end_date:
                        type: string
                        format: date-time
                        nullable: true
                        example: null
        '400':
          description: Invalid or missing subscription ID
        '500':
          description: Internal server error
  /v1/customer/checkDomainAvailability:
    get:
      summary: Check if a domain exists for a given brand and store
      tags:
        - Customer
      parameters:
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: Brand ID
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: domainId
          schema:
            type: string
          required: true
          description: Domain ID to check availability
      responses:
        '200':
          description: Domain availability check result
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '1'
                    description: 1 if domain exists, 0 if not
                  Message:
                    type: string
                    example: Domain exist
        '500':
          description: Internal server error or token/domain API failure
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '0'
                  Message:
                    type: string
                    example: Internal server error
  /v1/customer/getCustomerAgreement:
    get:
      summary: Get Customer Agreement
      description: >-
        Retrieves agreement details for a given customer based on brandId,
        storeId, and custId.
      tags:
        - Customer
      parameters:
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: The brand identifier.
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: The store identifier.
        - in: query
          name: custId
          schema:
            type: string
          required: true
          description: The customer ID (tenant).
      responses:
        '200':
          description: Agreement details retrieved successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalCount:
                    type: number
                  items:
                    type: array
                    items:
                      type: object
                      description: Agreement item
        '400':
          description: Validation failed.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                  message:
                    type: string
                  errors:
                    type: array
                    items:
                      type: string
        '500':
          description: Internal server error or external API failure.
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
                  statusCode:
                    type: number
  /v1/customer/getCustomerBillingInfo:
    get:
      summary: Get customer billing info from Microsoft
      tags:
        - Customer
      parameters:
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: Brand ID
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: tenantId
          schema:
            type: string
          required: true
          description: Tenant ID
      responses:
        '200':
          description: Successfully retrieved customer billing info
          content:
            application/json:
              schema:
                type: object
                properties:
                  Id:
                    type: string
                  Email:
                    type: string
                  Culture:
                    type: string
                  Language:
                    type: string
                  CompanyName:
                    type: string
                  DefaultAddress:
                    type: object
                  Links:
                    type: object
                  Attributes:
                    type: object
                  FirstName:
                    type: string
                  LastName:
                    type: string
                  Code:
                    type: integer
                  Description:
                    type: string
                  status:
                    type: boolean
        '400':
          description: Bad request due to validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: array
                    items:
                      type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Internal server error
  /v1/customer/getCustomerById:
    get:
      summary: Get customer details by type and tenant ID
      tags:
        - Customer
      parameters:
        - in: query
          name: type
          schema:
            type: string
            enum:
              - VALIDATE-ID
              - VALIDATE-DOMAIN
          required: true
          description: Validation type (either VALIDATE-ID or VALIDATE-DOMAIN)
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: Brand ID
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: tenantId
          schema:
            type: string
          required: true
          description: >-
            Microsoft Tenant ID (used as either ID or domain depending on
            validation type)
      responses:
        '200':
          description: Successfully retrieved customer information
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '1'
                  Message:
                    type: string
                    example: tenant.onmicrosoft.com
        '400':
          description: Bad request due to validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  errors:
                    type: array
                    items:
                      type: string
        '500':
          description: Internal server error or external endpoint failure
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  errors:
                    type: array
                    items:
                      type: string
  /v1/customer/getCustomerUsers:
    get:
      summary: Get Microsoft Customer Users
      tags:
        - Customer
      parameters:
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: Brand ID
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: tenantId
          schema:
            type: string
          required: true
          description: Microsoft Tenant ID
      responses:
        '200':
          description: Successfully retrieved customer users
          content:
            application/json:
              schema:
                type: object
                properties:
                  TotalCount:
                    type: integer
                  Items:
                    type: array
                    items:
                      type: object
                  Links:
                    type: object
                  Attributes:
                    type: object
                  Code:
                    type: integer
                  Description:
                    type: string
        '400':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                  message:
                    type: string
                  errors:
                    type: array
                    items:
                      type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                  message:
                    type: string
  /v1/customer/getCustomerValidationStatus:
    get:
      summary: Get Customer Validation Status
      tags:
        - Customer
      parameters:
        - in: query
          name: customerId
          schema:
            type: string
          required: true
          description: Customer ID
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
      responses:
        '200':
          description: Successfully fetched validation status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Customer is valid
                  data:
                    type: object
                    example:
                      customerId: cust-123
                      isValid: true
        '400':
          description: Bad request (Missing or invalid input)
        '500':
          description: Internal server error
  /v1/customer/getDirectSignStatusOfCustAgreement:
    get:
      summary: Get Direct Sign Status of Customer Agreement
      tags:
        - Customer
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Unique identifier of the store
        - in: query
          name: custTenantId
          required: true
          schema:
            type: string
          description: >-
            Tenant ID of the customer whose agreement status needs to be
            verified
      responses:
        '200':
          description: Successfully retrieved agreement sign status
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '1'
                  Message:
                    type: string
                    example: Agreement is signed
                  isSigned:
                    type: boolean
                    example: true
        '400':
          description: Validation error on missing or invalid parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '0'
                  Message:
                    type: string
                    example: The 'storeId' URL parameter is missing in the request.
        '500':
          description: Internal server error during processing
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '0'
                  Message:
                    type: string
                    example: Internal server error
  /v1/customer/searchCiscoCustomers:
    post:
      summary: Search Cisco Customers By Name
      description: Searches for Cisco customers by name and country code
      tags:
        - Customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                countryCode:
                  type: string
                  example: US
                name:
                  type: string
                  example: Example Corp
                storeId:
                  type: string
                  example: store123
                pageSize:
                  type: integer
                  example: 100
      responses:
        '200':
          description: Successfully retrieved Cisco customers
          content:
            application/json:
              schema:
                type: object
                properties:
                  bodID:
                    type: string
                    example: bod123
                  status:
                    type: string
                    example: SUCCESS
                  matchFound:
                    type: integer
                    example: 5
                  party:
                    type: array
                    items:
                      type: object
                      properties:
                        partyName:
                          type: string
                          example: Example Corp
                        localeCode:
                          type: string
                          example: US
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: string
                    example: Country code cannot be null or empty.
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAILURE
                  message:
                    type: string
                    example: 'An error occurred: [error details]'
  /v1/customer/setCustomerAgreement:
    post:
      summary: Set customer agreement
      tags:
        - Customer
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store ID
        - in: query
          name: brandId
          required: true
          schema:
            type: string
          description: Brand ID
        - in: query
          name: custId
          required: true
          schema:
            type: string
          description: Customer ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - primaryContact
                - dateAgreed
                - type
                - agreementLink
                - userId
              properties:
                primaryContact:
                  type: object
                  required:
                    - firstName
                    - lastName
                    - email
                  properties:
                    firstName:
                      type: string
                      example: John
                    lastName:
                      type: string
                      example: Doe
                    email:
                      type: string
                      example: <EMAIL>
                    phoneNumber:
                      type: string
                      example: '+1234567890'
                    middleName:
                      type: string
                      example: Michael
                    organizationRegistrationNumber:
                      type: string
                      example: ORG123456
                templateId:
                  type: string
                  description: >-
                    Optional - will be auto-populated from Microsoft API if not
                    provided
                  example: template-123
                dateAgreed:
                  type: string
                  format: date-time
                  example: '2025-07-09T10:59:55.895Z'
                type:
                  type: string
                  example: MicrosoftCustomerAgreement
                agreementLink:
                  type: string
                  example: https://example.com/agreement
                userId:
                  type: string
                  example: user-456
            example:
              primaryContact:
                firstName: string
                lastName: string
                email: string
                phoneNumber: string
                middleName: string
                organizationRegistrationNumber: string
              templateId: string
              dateAgreed: '2025-07-09T10:59:55.895Z'
              type: string
              agreementLink: string
              userId: string
      responses:
        '200':
          description: Customer agreement set successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
                  statusCode:
                    type: number
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                  message:
                    type: string
  /v1/customer/setCustomerBillingInfo:
    post:
      summary: Set customer billing information
      description: >-
        This API sets or updates the billing information of a customer for a
        specific brand, store, and tenant.
      tags:
        - Customer
      parameters:
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: The brand identifier
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: The store identifier
        - in: query
          name: tenantId
          schema:
            type: string
          required: true
          description: The tenant identifier
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                email:
                  type: string
                culture:
                  type: string
                language:
                  type: string
                companyName:
                  type: string
                defaultAddress:
                  type: object
                  properties:
                    country:
                      type: string
                    city:
                      type: string
                    addressLine1:
                      type: string
                    postalCode:
                      type: string
                    phoneNumber:
                      type: string
                    addressLine2:
                      type: string
                    firstName:
                      type: string
                    lastName:
                      type: string
                    state:
                      type: string
                links:
                  type: object
                  properties:
                    self:
                      type: object
                      properties:
                        uri:
                          type: string
                        method:
                          type: string
                    offer:
                      type: object
                      properties:
                        uri:
                          type: string
                        method:
                          type: string
                    next:
                      type: object
                      properties:
                        uri:
                          type: string
                        method:
                          type: string
                    availabilities:
                      type: object
                      properties:
                        uri:
                          type: string
                        method:
                          type: string
                    skus:
                      type: object
                      properties:
                        uri:
                          type: string
                        method:
                          type: string
                attributes:
                  type: object
                  properties:
                    objectType:
                      type: string
                    etag:
                      type: string
                firstName:
                  type: string
                lastName:
                  type: string
                code:
                  type: integer
                description:
                  type: string
      responses:
        '200':
          description: Billing info updated successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Bad request (validation or DTO errors)
        '500':
          description: Internal server error
  /v1/customer/upsertCustomer:
    post:
      summary: Upsert (create or update) a customer
      description: >-
        This API creates or updates a customer based on the input type (`CREATE`
        or `UPDATE`).
      tags:
        - Customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
                - custId
                - orgName
                - countryId
                - addressLine1
                - addressLine2
                - emailId
                - firstName
                - lastName
                - storeId
                - partnerId
                - userName
              properties:
                type:
                  type: string
                  enum:
                    - CREATE
                    - UPDATE
                custId:
                  type: string
                orgName:
                  type: string
                countryId:
                  type: string
                addressLine1:
                  type: string
                addressLine2:
                  type: string
                emailId:
                  type: string
                firstName:
                  type: string
                lastName:
                  type: string
                storeId:
                  type: string
                partnerId:
                  type: string
                userName:
                  type: string
                domainId:
                  type: string
                city:
                  type: string
                state:
                  type: string
                postalCode:
                  type: string
                brandId:
                  type: string
                id:
                  type: string
                middleName:
                  type: string
                vatId:
                  type: string
                phoneNumber:
                  type: string
                customerVertical:
                  type: string
                segment:
                  type: string
                sector:
                  type: string
                panNumber:
                  type: string
                newTenant:
                  type: boolean
                associatedPartnerId:
                  type: string
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                type: object
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Validation error
        '422':
          description: Business logic failure
        '500':
          description: Internal server error
  /v1/customer/validateAndSaveCiscoCustomer:
    post:
      summary: Validate and Save Cisco Customer
      description: Validates Cisco customer data and saves it to the database
      tags:
        - Customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                custId:
                  type: string
                  example: cust123
                storeId:
                  type: string
                  example: store123
                partyName:
                  type: string
                  example: Example Corp
                addressLine1:
                  type: string
                  example: 123 Main St
                addressLine2:
                  type: string
                  example: Suite 100
                city:
                  type: string
                  example: San Francisco
                state:
                  type: string
                  example: CA
                postalCode:
                  type: string
                  example: '94105'
                country:
                  type: string
                  example: United States
                countryCode:
                  type: string
                  example: US
                businessContactName:
                  type: string
                  example: John Doe
                businessContactEmail:
                  type: string
                  example: <EMAIL>
                businessContactNumber:
                  type: string
                  example: ******-123-4567
      responses:
        '200':
          description: Successfully validated and updated customer
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                  Message:
                    type: string
                    example: Customer updated successfully
                  ActionType:
                    type: string
                    example: UPDATE
        '201':
          description: Successfully validated and created customer
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                  Message:
                    type: string
                    example: Customer created successfully
                  ActionType:
                    type: string
                    example: INSERT
        '400':
          description: Bad request - validation errors or business logic failure
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: 'Validation failed: [error details]'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: 'An error occurred: [error details]'
  /v1/deliverySequence/getDeliverySequenceAddressesFromSapByPartner:
    post:
      summary: Get delivery sequence addresses from SAP by business partner
      tags:
        - DeliverySequence
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - customerCode
                - companyCode
                - storeId
              properties:
                customerCode:
                  type: string
                  description: Unique customer code
                companyCode:
                  type: string
                  description: SAP company code
                storeId:
                  type: string
                  description: Store identifier
              example:
                customerCode: '123456'
                companyCode: A1
                storeId: Store-789
      responses:
        '200':
          description: Successfully retrieved delivery sequence addresses
          content:
            application/json:
              schema:
                type: object
                properties:
                  httpStatusCode:
                    type: integer
                    example: 200
                  content:
                    type: object
                    properties:
                      status:
                        type: string
                        example: SUCCESS
                      message:
                        type: string
                        example: Delivery addresses retrieved
                      customerParties:
                        type: array
                        items:
                          type: object
                          properties:
                            partnerType:
                              type: string
                              example: AG
                            partnerCode:
                              type: string
                              example: P12345
                            name:
                              type: string
                              example: John Enterprises
                            address1:
                              type: string
                              example: 123 Main Street
                            address2:
                              type: string
                              example: Suite 400
                            address3:
                              type: string
                              example: ''
                            city:
                              type: string
                              example: Mumbai
                            state:
                              type: string
                              example: MH
                            pinCode:
                              type: string
                              example: '400001'
                            paymentTermsCode:
                              type: string
                              example: PT30
                            paymentType:
                              type: string
                              example: Credit
        '400':
          description: Bad request. Input validation failed.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: string
                    example: customerCode is required
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: string
                    example: Internal server error
  /v1/entitlement/getAzurePlanEntitlements:
    get:
      summary: Get Azure Plan Entitlements
      description: Retrieves entitlements for a given Azure Plan subscription.
      tags:
        - Entitlement
      parameters:
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
          description: Azure subscription ID
          example: sub-123456
      responses:
        '200':
          description: Successfully retrieved entitlements
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                      example: ent-001
                    friendlyName:
                      type: string
                      example: Visual Studio Enterprise
                    status:
                      type: string
                      example: Active
                    subscriptionId:
                      type: string
                      example: sub-123456
        '204':
          description: No entitlements found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal Server Error
                  isError:
                    type: boolean
                    example: true
  /v1/entitlement/getEntitlementDetails:
    post:
      summary: Get entitlement details for an order line item
      tags:
        - Entitlement
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - orderLineKey
              properties:
                orderLineKey:
                  type: integer
                  minimum: 1
                  description: The ID of the order line item
              example:
                orderLineKey: 12345
      responses:
        '200':
          description: Entitlement details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  includedEntitlements:
                    type: array
                    items:
                      type: object
                  referenceOrder:
                    type: object
                    properties:
                      id:
                        type: string
                      alternateId:
                        type: string
                  productId:
                    type: string
                  quantity:
                    type: integer
                  quantityDetails:
                    type: array
                    items:
                      type: object
                  entitledArtifacts:
                    type: array
                    items:
                      type: object
                  skuId:
                    type: string
                  entitlementType:
                    type: string
                  fulfillmentState:
                    type: string
                  expiryDate:
                    type: string
                    format: date-time
                  subscriptionId:
                    type: string
                  hasAddOn:
                    type: boolean
                  subscriptionStatus:
                    type: string
                  startDate:
                    type: string
                    format: date-time
        '204':
          description: No entitlement found for the given order line key
        '400':
          description: Bad request. Validation failed.
          content:
            application/json:
              schema:
                type: object
                properties:
                  isError:
                    type: boolean
                    example: true
                  message:
                    type: string
                  statusCode:
                    type: integer
                    example: 400
        '422':
          description: Service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  isError:
                    type: boolean
                    example: true
                  message:
                    type: string
                  statusCode:
                    type: integer
                    example: 422
  /v1/entitlement/getProvisionalStatusData:
    get:
      summary: Get provisional status data for a customer order
      tags:
        - Entitlement
      parameters:
        - in: query
          name: customerId
          required: true
          schema:
            type: string
          description: Customer ID
          example: cust-123
        - in: query
          name: orderId
          required: true
          schema:
            type: string
          description: Order ID
          example: order-456
        - in: query
          name: token
          required: true
          schema:
            type: string
          description: Authorization token
          example: bearer-token-xyz
      responses:
        '200':
          description: Provisional status retrieved successfully
          content:
            text/plain:
              schema:
                type: string
                example: Active
                description: The provisional status of the order
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 400
                  isError:
                    type: boolean
                    example: true
        '422':
          description: Service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  statusCode:
                    type: number
                    example: 422
                  isError:
                    type: boolean
                    example: true
  /v1/entitlement/getSubscriptionEntitlements:
    get:
      summary: Get Subscription Entitlements
      description: Retrieves entitlements for a given subscription.
      tags:
        - Entitlement
      parameters:
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
          description: Subscription ID
          example: sub-123456
      responses:
        '200':
          description: Successfully retrieved subscription entitlements
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalCount:
                    type: integer
                    example: 2
                  entitlements:
                    type: array
                    items:
                      type: object
                      properties:
                        subscriptionId:
                          type: string
                          example: sub-123456
                        customerId:
                          type: string
                          example: cust-789
                        entitlementId:
                          type: string
                          example: ent-001
                        friendlyName:
                          type: string
                          example: Microsoft 365 Business Premium
                        status:
                          type: string
                          example: Active
        '204':
          description: No entitlements found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal Server Error
                  isError:
                    type: boolean
                    example: true
  /v1/googleCustomer/createCustomer/{custId}/{partnerId}/{storeId}:
    post:
      summary: Create Google Customer
      description: >-
        Creates a new Google customer or links an existing customer based on
        domain availability and ownership status. Handles both new customer
        creation and existing customer linking scenarios.
      tags:
        - GoogleCustomer
      parameters:
        - in: path
          name: custId
          required: true
          schema:
            type: string
          description: Customer ID from the internal system
          example: CUST123456
        - in: path
          name: partnerId
          required: true
          schema:
            type: string
          description: Partner ID associated with the customer
          example: PARTNER789
        - in: path
          name: storeId
          required: true
          schema:
            type: string
          description: Store ID where the customer is being created
          example: STORE001
        - in: query
          name: isExistingCustomer
          required: true
          schema:
            type: boolean
          description: >-
            Flag indicating whether this is an existing customer (true) or new
            customer (false)
          example: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - brandIds
              properties:
                brandIds:
                  type: array
                  items:
                    type: string
                  description: Array of brand IDs associated with the customer
                  example:
                    - BRAND001
                    - BRAND002
                orgDisplayName:
                  type: string
                  description: Organization display name
                  example: Acme Corporation
                orgPostalAddress:
                  type: object
                  properties:
                    revision:
                      type: integer
                      description: Address revision number
                      example: 1
                    regionCode:
                      type: string
                      description: Region code (e.g., country code)
                      example: US
                    languageCode:
                      type: string
                      description: Language code for the address
                      example: en
                    postalCode:
                      type: string
                      description: Postal/ZIP code
                      example: '12345'
                    sortingCode:
                      type: string
                      description: Sorting code for mail delivery
                      example: ABC123
                    administrativeArea:
                      type: string
                      description: State or administrative area
                      example: California
                    locality:
                      type: string
                      description: City or locality
                      example: San Francisco
                    sublocality:
                      type: string
                      description: Sublocality or district
                      example: Downtown
                    addressLines:
                      type: array
                      items:
                        type: string
                      description: Array of address lines
                      example:
                        - 123 Main St
                        - Suite 100
                    recipients:
                      type: array
                      items:
                        type: string
                      description: Array of recipients
                      example:
                        - John Doe
                    organization:
                      type: string
                      description: Organization name for the address
                      example: Acme Corporation
                primaryContactInfo:
                  type: object
                  properties:
                    firstName:
                      type: string
                      description: Primary contact first name
                      example: John
                    lastName:
                      type: string
                      description: Primary contact last name
                      example: Doe
                    displayName:
                      type: string
                      description: Primary contact display name
                      example: John Doe
                    email:
                      type: string
                      format: email
                      description: Primary contact email address
                      example: <EMAIL>
                    title:
                      type: string
                      description: Primary contact job title
                      example: IT Administrator
                    phone:
                      type: string
                      description: Primary contact phone number
                      example: ******-123-4567
                alternateEmail:
                  type: string
                  format: email
                  description: Alternate email address
                  example: <EMAIL>
                domain:
                  type: string
                  description: Domain name for the customer
                  example: acme.com
                languageCode:
                  type: string
                  description: Preferred language code
                  example: en
                cloudIdentityInfo:
                  type: object
                  properties:
                    customerType:
                      type: string
                      description: Type of customer (e.g., business, education)
                      example: business
                    primaryDomain:
                      type: string
                      description: Primary domain for cloud identity
                      example: acme.com
                    isDomainVerified:
                      type: string
                      description: Domain verification status
                      example: 'true'
                    alternateEmail:
                      type: string
                      format: email
                      description: Alternate email for cloud identity
                      example: <EMAIL>
                    phoneNumber:
                      type: string
                      description: Phone number for cloud identity
                      example: ******-987-6543
                    languageCode:
                      type: string
                      description: Language code for cloud identity
                      example: en
                    adminConsoleUri:
                      type: string
                      description: Admin console URI
                      example: https://admin.google.com
                    eduData:
                      type: object
                      properties:
                        instituteType:
                          type: string
                          description: Type of educational institution
                          example: university
                        instituteSize:
                          type: string
                          description: Size of the educational institution
                          example: large
                        website:
                          type: string
                          description: Institution website URL
                          example: https://www.acme-university.edu
                channelPartnerId:
                  type: string
                  description: Channel partner ID
                  example: CP123456
                correlationId:
                  type: string
                  description: Correlation ID for request tracking
                  example: corr-123-456-789
                googleCustomerId:
                  type: string
                  description: Existing Google customer ID (for existing customers)
                  example: google-customer-123
                cloudIdentityId:
                  type: string
                  description: Existing cloud identity ID (for existing customers)
                  example: cloud-identity-456
      responses:
        '200':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Customer created successfully
                  Data:
                    type: object
                    properties:
                      googleCustomerId:
                        type: string
                        example: customer-123
                      operationId:
                        type: string
                        example: operation-456
                      operationType:
                        type: string
                        example: CREATE_CUSTOMER
                      cloudIdentityId:
                        type: string
                        nullable: true
                        example: cloud-identity-789
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
        '400':
          description: Bad Request - Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: >-
                      BrandId cannot be null or empty, Store with ID 'invalid'
                      does not exist
                  Data:
                    type: object
                    nullable: true
                    example: null
                  StatusCode:
                    type: integer
                    example: 400
                  IsError:
                    type: boolean
                    example: true
        '422':
          description: Unprocessable Entity - Service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: Domain verification failed'
                  Data:
                    type: object
                    nullable: true
                    example: null
                  StatusCode:
                    type: integer
                    example: 422
                  IsError:
                    type: boolean
                    example: true
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: Internal server error'
                  Data:
                    type: object
                    nullable: true
                    example: null
                  StatusCode:
                    type: integer
                    example: 500
                  IsError:
                    type: boolean
                    example: true
  /v1/googleCustomer/getGoogleCustomerByDomain:
    post:
      summary: Verify and Get Google Customer By Domain
      description: >-
        verify domain and the get customer details from Google Method is
        HTTPPOST post because we are combining HTTPPOST(verify call) and
        HTTPGET(detail call) in single action (to save the magento call)
      tags:
        - GoogleCustomer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                storeId:
                  type: string
                  example: AE-EN
                domain:
                  type: string
                  example: example.com
                isExisting:
                  type: boolean
                  example: true
                channelPartnerId:
                  type: string
                  example: channel-partner-123
      responses:
        '200':
          description: Successfully retrieved Google customer details
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Success
                  Data:
                    type: object
                    description: Google customer details
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: StoreId cannot be null or empty
                  IsError:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 400
        '422':
          description: Unprocessable entity - service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: [error details]'
                  IsError:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 422
  /v1/googleCustomer/getGoogleCustomerById:
    get:
      summary: Get Google Customer By ID
      description: Retrieves Google customer details by store ID and Google customer ID
      tags:
        - GoogleCustomer
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store identifier
          example: AE-EN
        - in: query
          name: googleCustomerId
          required: true
          schema:
            type: string
          description: Google customer identifier
          example: customers/C12345678
      responses:
        '200':
          description: Successfully retrieved Google customer details
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Success
                  Data:
                    type: object
                    description: Google customer details
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: StoreId can't be null or empty
                  IsError:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 400
        '422':
          description: Unprocessable entity - service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: [error details]'
                  IsError:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 422
  /v1/googleCustomer/getGoogleCustomerDetails:
    get:
      summary: Get Google Customer Details
      description: Retrieves Google customer details by customer ID (custId)
      tags:
        - GoogleCustomer
      parameters:
        - in: query
          name: custId
          required: true
          schema:
            type: string
          description: Magento Customer ID
          example: customer123
      responses:
        '200':
          description: Successfully retrieved Google customer details
          content:
            application/json:
              schema:
                type: object
                properties:
                  ChannelPartnerId:
                    type: string
                    example: C02a7qnez
                  CloudIdentityId:
                    type: string
                    example: C04g3z3oh
                  Domain:
                    type: string
                    example: goog-test.rilcsp.in.18Jul2025-1.com
                  CustomerType:
                    type: string
                    example: DOMAIN
                  CompanyName:
                    type: string
                    example: Google 3 GWF P0T0
                  FirstName:
                    type: string
                    example: Google 3 GWF P0T0
                  LastName:
                    type: string
                    example: fg
                  Email:
                    type: string
                    example: <EMAIL>
                  AlternateEmail:
                    type: string
                    example: <EMAIL>
                  RegionCode:
                    type: string
                    example: IN
                  AdministrativeArea:
                    type: string
                    example: Maharashtra
                  Locality:
                    type: string
                    example: Pune
                  AddressLine1:
                    type: string
                    example: gdfgfd
                  AddressLine2:
                    type: string
                    nullable: true
                    example: null
                  AddressLine3:
                    type: string
                    nullable: true
                    example: null
                  PostalCode:
                    type: string
                    example: '411057'
                  InstituteType:
                    type: string
                    nullable: true
                    example: null
                  InstituteSize:
                    type: string
                    nullable: true
                    example: null
                  Website:
                    type: string
                    nullable: true
                    example: null
                  PhoneNumber:
                    type: string
                    example: '+917867868686'
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
                    example:
                      - CustId can't be null or empty
                  statusCode:
                    type: integer
                    example: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
                    example:
                      - 'An error occurred: internal service failure'
                  statusCode:
                    type: integer
                    example: 500
  /v1/googleCustomer/provisionCloudIdentity:
    post:
      summary: Provision Google Cloud Identity
      description: >-
        Validates and provisions a Google Cloud Identity for the given store and
        Google customer ID.
      tags:
        - GoogleCustomer
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store identifier
          example: AE-EN
        - in: query
          name: googleCustomerId
          required: true
          schema:
            type: string
          description: Google customer identifier
          example: customers/C12345678
      responses:
        '200':
          description: Google Cloud Identity provisioned successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Provisioning successful
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: StoreId can't be null or empty
                  StatusCode:
                    type: integer
                    example: 400
                  IsError:
                    type: boolean
                    example: true
        '422':
          description: Unprocessable entity - internal service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: Unexpected server error'
                  StatusCode:
                    type: integer
                    example: 422
                  IsError:
                    type: boolean
                    example: true
  /v1/googleCustomer/registerCustomerWithGoogle/store/{storeId}:
    post:
      summary: Register Customer With Google
      description: >-
        Registers a new customer directly with Google Cloud Identity API. This
        endpoint creates a customer in Google's system and returns the customer
        details including the generated customer ID.
      tags:
        - GoogleCustomer
      parameters:
        - in: path
          name: storeId
          required: true
          schema:
            type: string
          description: Store ID where the customer is being registered
          example: STORE001
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - brandIds
              properties:
                brandIds:
                  type: array
                  items:
                    type: string
                  description: Array of brand IDs associated with the customer
                  example:
                    - BRAND001
                    - BRAND002
                orgDisplayName:
                  type: string
                  description: Organization display name
                  example: Acme Corporation
                orgPostalAddress:
                  type: object
                  properties:
                    revision:
                      type: integer
                      description: Address revision number
                      example: 1
                    regionCode:
                      type: string
                      description: Region code (e.g., country code)
                      example: US
                    languageCode:
                      type: string
                      description: Language code for the address
                      example: en
                    postalCode:
                      type: string
                      description: Postal/ZIP code
                      example: '12345'
                    sortingCode:
                      type: string
                      description: Sorting code for mail delivery
                      example: ABC123
                    administrativeArea:
                      type: string
                      description: State or administrative area
                      example: California
                    locality:
                      type: string
                      description: City or locality
                      example: San Francisco
                    sublocality:
                      type: string
                      description: Sublocality or district
                      example: Downtown
                    addressLines:
                      type: array
                      items:
                        type: string
                      description: Array of address lines
                      example:
                        - 123 Main St
                        - Suite 100
                    recipients:
                      type: array
                      items:
                        type: string
                      description: Array of recipients
                      example:
                        - John Doe
                    organization:
                      type: string
                      description: Organization name for the address
                      example: Acme Corporation
                primaryContactInfo:
                  type: object
                  properties:
                    firstName:
                      type: string
                      description: Primary contact first name
                      example: John
                    lastName:
                      type: string
                      description: Primary contact last name
                      example: Doe
                    displayName:
                      type: string
                      description: Primary contact display name
                      example: John Doe
                    email:
                      type: string
                      format: email
                      description: Primary contact email address
                      example: <EMAIL>
                    title:
                      type: string
                      description: Primary contact job title
                      example: IT Administrator
                    phone:
                      type: string
                      description: Primary contact phone number
                      example: ******-123-4567
                alternateEmail:
                  type: string
                  format: email
                  description: Alternate email address
                  example: <EMAIL>
                domain:
                  type: string
                  description: Domain name for the customer
                  example: acme.com
                languageCode:
                  type: string
                  description: Preferred language code
                  example: en
                cloudIdentityInfo:
                  type: object
                  properties:
                    customerType:
                      type: string
                      description: Type of customer (e.g., business, education)
                      example: business
                    primaryDomain:
                      type: string
                      description: Primary domain for cloud identity
                      example: acme.com
                    isDomainVerified:
                      type: string
                      description: Domain verification status
                      example: 'true'
                    alternateEmail:
                      type: string
                      format: email
                      description: Alternate email for cloud identity
                      example: <EMAIL>
                    phoneNumber:
                      type: string
                      description: Phone number for cloud identity
                      example: ******-987-6543
                    languageCode:
                      type: string
                      description: Language code for cloud identity
                      example: en
                    adminConsoleUri:
                      type: string
                      description: Admin console URI
                      example: https://admin.google.com
                    eduData:
                      type: object
                      properties:
                        instituteType:
                          type: string
                          description: Type of educational institution
                          example: university
                        instituteSize:
                          type: string
                          description: Size of the educational institution
                          example: large
                        website:
                          type: string
                          description: Institution website URL
                          example: https://www.acme-university.edu
                channelPartnerId:
                  type: string
                  description: Channel partner ID
                  example: CP123456
                correlationId:
                  type: string
                  description: Correlation ID for request tracking
                  example: corr-123-456-789
                googleCustomerId:
                  type: string
                  description: Existing Google customer ID (optional)
                  example: google-customer-123
                cloudIdentityId:
                  type: string
                  description: Existing cloud identity ID (optional)
                  example: cloud-identity-456
      responses:
        '200':
          description: Customer registered successfully with Google
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Success
                  Data:
                    type: object
                    properties:
                      Name:
                        type: string
                        example: customers/C12345678
                      CustomerId:
                        type: string
                        example: C12345678
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
        '400':
          description: Bad Request - Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: >-
                      BrandId cannot be null or empty, Store with ID 'invalid'
                      does not exist
                  Data:
                    type: object
                    nullable: true
                    example: null
                  StatusCode:
                    type: integer
                    example: 400
                  IsError:
                    type: boolean
                    example: true
        '422':
          description: Unprocessable Entity - Google API error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Domain already exists in Google
                  Data:
                    type: object
                    nullable: true
                    example: null
                  StatusCode:
                    type: integer
                    example: 422
                  IsError:
                    type: boolean
                    example: true
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: Internal server error'
                  Data:
                    type: object
                    nullable: true
                    example: null
                  StatusCode:
                    type: integer
                    example: 500
                  IsError:
                    type: boolean
                    example: true
  /v1/googleCustomer/validateGoogleEndCustomer:
    post:
      summary: Validate Google End Customer
      description: >-
        Validates Google end customer details including CP ID, customer mapping,
        and offer availability
      tags:
        - GoogleCustomer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                storeId:
                  type: string
                  example: AE-EN
                brandId:
                  type: string
                  example: '1'
                partnerId:
                  type: string
                  example: partner123
                productId:
                  type: string
                  example: product123
                skuId:
                  type: string
                  example: sku123
                offerId:
                  type: string
                  example: offer123
                custId:
                  type: string
                  example: customer123
                cpId:
                  type: string
                  example: CP12345678
                googleCustomerId:
                  type: string
                  example: customers/C12345678
                cloudIdentityId:
                  type: string
                  example: cloud123
      responses:
        '200':
          description: Validation completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Success
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: StoreId cannot be null or empty
                  IsError:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 400
        '422':
          description: Unprocessable entity - service error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: [error details]'
                  IsError:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 422
  /v1/googleCustomer/verifyDomainExists:
    post:
      summary: Verify if a domain exists and is linkable
      description: >-
        Validates the input and checks if a given domain is available and
        linkable for a store.
      tags:
        - GoogleCustomer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                storeId:
                  type: string
                  example: AE-EN
                domain:
                  type: string
                  example: example.com
                isExisting:
                  type: boolean
                  example: true
                channelPartnerId:
                  type: string
                  example: channel-partner-123
      responses:
        '200':
          description: Domain verification successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Domain is available and linkable
                  IsAvailable:
                    type: boolean
                    example: true
                  IsLinkable:
                    type: boolean
                    example: true
                  StatusCode:
                    type: integer
                    example: 200
        '400':
          description: Bad request - validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: StoreId must not be empty
                  IsAvailable:
                    type: boolean
                    example: false
                  IsLinkable:
                    type: boolean
                    example: false
                  StatusCode:
                    type: integer
                    example: 400
        '422':
          description: Unprocessable entity - internal error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: 'An error occurred: Unexpected server error'
                  IsAvailable:
                    type: boolean
                    example: false
                  IsLinkable:
                    type: boolean
                    example: false
                  StatusCode:
                    type: integer
                    example: 422
  /v1/googleOffer/getPurchasableOffer:
    get:
      summary: Get Google Purchasable Offer by Offer ID
      description: >-
        Retrieves a specific purchasable offer for a given store and Google
        customer based on the provided identifiers.
      tags:
        - Google Offers
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store identifier
          example: store-001
        - in: query
          name: googleCustomerId
          required: true
          schema:
            type: string
          description: Google customer ID
          example: cust-abc-123
        - in: query
          name: productId
          required: true
          schema:
            type: string
          description: Google product ID
          example: google-cloud-product
        - in: query
          name: skuId
          required: true
          schema:
            type: string
          description: Google SKU ID
          example: sku-987
        - in: query
          name: offerId
          required: true
          schema:
            type: string
          description: Google Offer ID
          example: offer-xyz
      responses:
        '200':
          description: Successfully retrieved the purchasable offer
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                    example: Success
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
                  Data:
                    type: object
                    nullable: true
                    description: The matched offer details (if found)
        '404':
          description: Offer not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
                  isError:
                    type: boolean
                    example: true
  /v1/indirectReseller/getIndirectResellerById:
    get:
      summary: Get Indirect Reseller By ID
      description: Retrieves details of an indirect reseller for a given store.
      tags:
        - IndirectReseller
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Unique identifier of the store.
          example: store-123
        - in: query
          name: indirectResellerId
          required: true
          schema:
            type: string
          description: Unique identifier of the indirect reseller.
          example: reseller-456
      responses:
        '200':
          description: Successfully retrieved indirect reseller details.
          content:
            application/json:
              schema:
                type: object
                properties:
                  Id:
                    type: string
                    example: reseller-456
                  Name:
                    type: string
                    example: ABC Reseller Ltd.
                  RelationshipType:
                    type: string
                    example: Indirect
                  State:
                    type: string
                    example: Active
                  MpnId:
                    type: string
                    example: '1234567'
                  Location:
                    type: string
                    example: IN
                  ErrorMessage:
                    type: string
                    example: ''
        '400':
          description: Bad request due to missing or invalid parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  ErrorMessage:
                    type: string
                    example: >-
                      Store Id cannot be null or empty, Indirect Reseller Id
                      cannot be null or empty
        '500':
          description: Internal server error.
          content:
            application/json:
              schema:
                type: object
                properties:
                  ErrorMessage:
                    type: string
                    example: Internal Server Error
  /v1/indirectReseller/saveIndirectReseller:
    post:
      summary: Save Indirect Reseller
      description: >-
        Saves or registers an indirect reseller with required partner, store,
        and reseller details.
      tags:
        - IndirectReseller
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - partnerId
                - storeId
                - vendor
                - irName
                - irId
                - mpnId
                - location
              properties:
                partnerId:
                  type: string
                  description: Unique identifier of the partner
                  example: '13331'
                storeId:
                  type: string
                  description: Unique identifier of the store
                  example: AE-EN
                vendor:
                  type: string
                  description: Vendor name associated with the reseller
                  example: Microsoft
                irName:
                  type: string
                  description: Name of the indirect reseller
                  example: Clementine Technologies
                irId:
                  type: string
                  description: Unique ID of the indirect reseller
                  example: d1716a0f-835d-4210-94ea-c26b8c3f677b
                mpnId:
                  type: string
                  description: Microsoft Partner Network ID
                  example: '6363276'
                location:
                  type: string
                  description: Location or country code of the indirect reseller
                  example: AE
      responses:
        '200':
          description: Indirect reseller saved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                  Message:
                    type: string
                    example: Indirect reseller saved successfully
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: partnerId is required, storeId is required
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: Internal Server Error
  /v1/linkedAccount/delinkLinkedAccount:
    put:
      summary: Delink a linked account by subscription ID
      description: >-
        Removes the association of a linked account using the provided
        subscription ID.
      tags:
        - LinkedAccount
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - linkedAccountId
              properties:
                linkedAccountId:
                  type: string
                  description: >-
                    The subscription ID or linked account identifier to be
                    delinked
                  example: SUB123456
      responses:
        '200':
          description: Successfully delinked the account
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                  Message:
                    type: string
                    example: Linked account delinked successfully
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: array
                    items:
                      type: string
                    example:
                      - Linked account id cannot be null or empty
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: string
                    example: Internal server error
  /v1/linkedAccount/getLinkedAccountDetails:
    get:
      summary: Get linked account details by subscription ID
      description: >-
        Retrieves linked account details using the provided subscription ID
        (linkedAccountId).
      tags:
        - LinkedAccount
      parameters:
        - in: query
          name: linkedAccountId
          required: true
          schema:
            type: string
          description: Linked account ID (usually the subscription ID)
          example: SUB123456
      responses:
        '200':
          description: Successfully retrieved the linked account details
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: PASS
                  message:
                    type: object
                    properties:
                      PartnerName:
                        type: string
                      SapCompany:
                        type: string
                      EmailId:
                        type: string
                      StoreID:
                        type: string
                      CustName:
                        type: string
                      SubscriptionId:
                        type: string
                      CustEmail:
                        type: string
                      SapCustId:
                        type: string
                      PartnerId:
                        type: string
                      CustId:
                        type: string
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: array
                    items:
                      type: string
                    example:
                      - Linked account id cannot be null or empty
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: string
                    example: Internal server error
  /v1/materialPrice/getMaterialPrice:
    get:
      summary: Get material pricing details
      tags:
        - MaterialPrice
      parameters:
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: materialId
          schema:
            type: string
          required: true
          description: Material ID
        - in: query
          name: brandName
          schema:
            type: string
          required: true
          description: Brand Name
        - in: query
          name: billType
          schema:
            type: string
          required: true
          description: Bill Type
        - in: query
          name: segment
          schema:
            type: string
          required: true
          description: Segment
        - in: query
          name: term
          schema:
            type: string
          required: true
          description: Term
      responses:
        '200':
          description: Successfully fetched material price
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Message:
                    type: string
                  CostPrice:
                    type: number
                  ListPrice:
                    type: number
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '0'
                  Message:
                    type: string
                    example: Internal server error
  /v1/reconciliation/export:
    post:
      summary: Export reconciliation data to Microsoft
      tags:
        - Reconciliation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - brandId
                - invoiceId
                - attributeSet
              properties:
                storeId:
                  type: string
                brandId:
                  type: string
                invoiceId:
                  type: string
                attributeSet:
                  type: string
      responses:
        '200':
          description: Reconciliation export successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  Message:
                    type: string
                  StatusCode:
                    type: integer
                  IsError:
                    type: boolean
                  Data:
                    type: object
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /v1/renewal/createRenewalRequestMS:
    post:
      summary: Create renewal request for Microsoft subscriptions
      tags:
        - Renewal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - skey
                - brandId
                - storeId
              properties:
                skey:
                  type: integer
                  description: Subscription key identifier
                  example: 12345
                brandId:
                  type: string
                  description: Brand identifier
                  example: '1002'
                storeId:
                  type: string
                  description: Store identifier
                  example: STORE001
      responses:
        '200':
          description: Renewal request created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                  MSResponse:
                    type: object
                    description: Microsoft subscription response
                  MSErrorResponse:
                    type: object
                    description: Microsoft error response if any
                  ErrorMessage:
                    type: string
                    description: Error message if any
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  ErrorMessage:
                    type: string
                    example: Validation errors
        '500':
          description: Internal server error
  /v1/renewal/getGoogleRenewalDetails:
    get:
      summary: Fetch Google Renewal Details for a given entitlement
      tags:
        - Renewal
      parameters:
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: googleCustomerId
          schema:
            type: string
          required: true
          description: Google Customer ID
        - in: query
          name: entitlementId
          schema:
            type: string
          required: true
          description: Google Entitlement ID
      responses:
        '200':
          description: Google renewal details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  StatusCode:
                    type: integer
                    example: 200
                  IsError:
                    type: boolean
                    example: false
                  Message:
                    type: string
                    example: Details fetched successfully
                  Data:
                    type: object
                    description: Renewal details payload
        '400':
          description: Validation error in request parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  StatusCode:
                    type: integer
                    example: 400
                  IsError:
                    type: boolean
                    example: true
                  Message:
                    type: string
                    example: storeId is required
        '500':
          description: Internal server error or Google API failure
          content:
            application/json:
              schema:
                type: object
                properties:
                  StatusCode:
                    type: integer
                    example: 500
                  IsError:
                    type: boolean
                    example: true
                  Message:
                    type: string
                    example: Internal server error
  /v1/renewal/getRenewalPromotion:
    get:
      summary: Get renewal promotion details
      tags:
        - Renewal
      parameters:
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
          description: Subscription ID
          example: sub-123456
        - in: query
          name: billType
          required: true
          schema:
            type: string
          description: Billing type
          example: Monthly
        - in: query
          name: term
          required: true
          schema:
            type: string
          description: Term duration
          example: P1M
        - in: query
          name: brandId
          required: true
          schema:
            type: integer
          description: Brand ID
          example: 1001
        - in: query
          name: materialno
          required: true
          schema:
            type: string
          description: Material number
          example: MAT123
        - in: query
          name: quantity
          required: true
          schema:
            type: integer
          description: Quantity
          example: 5
        - in: query
          name: segment
          required: true
          schema:
            type: string
          description: Segment
          example: Commercial
        - in: query
          name: customerTermEndDate
          required: true
          schema:
            type: string
            format: date-time
          description: Customer term end date
          example: '2024-12-31T23:59:59Z'
        - in: query
          name: isOldSubscriptionModified
          required: true
          schema:
            type: boolean
          description: Whether old subscription is modified
          example: false
      responses:
        '200':
          description: Renewal promotion details retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        '500':
          description: Internal server error
  /v1/renewal/upsertRenewalData:
    post:
      summary: Upsert (create or update) renewal data
      description: >-
        This API validates and processes renewal data for subscriptions based on
        brand and subscription details. It handles business validations for
        legacy and modified subscriptions.
      tags:
        - Renewal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - BrandId
                - AutoRenewStatus
                - MaterialNo
                - PlanType
                - TermDuration
                - PromotionId
                - Quantity
                - SubscriptionId
              properties:
                BrandId:
                  type: string
                AutoRenewStatus:
                  type: boolean
                MaterialNo:
                  type: string
                PlanType:
                  type: string
                TermDuration:
                  type: string
                PromotionId:
                  type: string
                Quantity:
                  type: integer
                CustomTermEndDate:
                  type: string
                  format: date-time
                SubscriptionId:
                  type: string
                DiscountPercentage:
                  type: number
                PromotionRate:
                  type: number
                IsOldSubscriptionModified:
                  type: boolean
                LegacyBrandId:
                  type: string
                NCEBrandId:
                  type: string
                SSBrandId:
                  type: string
                TaxPercentage:
                  type: number
                CustomTermType:
                  type: string
                CustomTermPrice:
                  type: number
                FirstScheduleEndDate:
                  type: string
                  format: date-time
                QuantityType:
                  type: string
                GoogleBrandIds:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Renewal data processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Message:
                    type: string
        '400':
          description: Validation failed or business rule violation
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Message:
                    type: string
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Message:
                    type: string
        '500':
          description: Internal server error
  /v1/renewal/validateRenewalRequest:
    get:
      summary: Validate if a renewal request is valid for a given subscription
      tags:
        - Renewal
      parameters:
        - in: query
          name: subscriptionId
          schema:
            type: string
          required: true
          description: Subscription ID to validate for renewal
      responses:
        '200':
          description: Renewal validation status
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                    description: PASS if valid, FAIL otherwise
                  Message:
                    type: string
                    example: Renewal is valid
        '400':
          description: Invalid input provided
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: Invalid subscriptionId
        '500':
          description: Internal server error or unexpected failure
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: Internal Server Error
  /v1/reseller/addUpdateCiscoReseller:
    post:
      summary: Add or update a Cisco Reseller
      description: >-
        This API adds a new reseller or updates an existing one based on the
        input.
      tags:
        - Reseller
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - partnerId
                - partyName
                - addressLine1
                - city
                - state
                - postalCode
                - country
                - businessContactName
                - businessContactEmail
                - businessContactNumber
                - accountNumber
                - isActive
              properties:
                storeId:
                  type: string
                partnerId:
                  type: string
                partyName:
                  type: string
                addressLine1:
                  type: string
                addressLine2:
                  type: string
                city:
                  type: string
                state:
                  type: string
                postalCode:
                  type: string
                country:
                  type: string
                businessContactName:
                  type: string
                businessContactEmail:
                  type: string
                businessContactNumber:
                  type: string
                accountNumber:
                  type: string
                isActive:
                  type: boolean
            example:
              storeId: '1234'
              partnerId: '5678'
              partyName: Cisco Partner
              addressLine1: 123 Tech Park
              addressLine2: Suite 456
              city: San Jose
              state: CA
              postalCode: '95134'
              country: USA
              businessContactName: John Doe
              businessContactEmail: <EMAIL>
              businessContactNumber: +1-408-555-1212
              accountNumber: ACCT123456
              isActive: true
      responses:
        '200':
          description: Reseller updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Message:
                    type: string
                  ActionType:
                    type: string
        '201':
          description: Reseller created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Message:
                    type: string
                  ActionType:
                    type: string
        '400':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: FAIL
                  message:
                    type: string
                    example: Validation error details
        '422':
          description: Business logic failure
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: Unable to insert/update reseller
        '500':
          description: Internal server error
  /v1/SAPInvoiceDocument/getSAPInvoiceDocument:
    get:
      summary: Get SAP Invoice Document
      tags:
        - SAP Invoice Document
      parameters:
        - in: query
          name: InvoiceNo
          required: true
          schema:
            type: string
            maxLength: 10
          description: Invoice number to retrieve document for
      responses:
        '200':
          description: SAP Invoice Document retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: PASS
                  Message:
                    type: string
                    example: ''
                  Content:
                    type: string
                    description: Invoice document content from S3
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: InvoiceNo is required
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  Message:
                    type: string
                    example: Internal server error
  /v1/subscription/autoRenewSubscription:
    post:
      summary: Toggle Auto-Renewal for a Subscription
      tags:
        - Subscription
      parameters:
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
          description: Subscription ID to update
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store ID
        - in: query
          name: autoRenew
          required: true
          schema:
            type: boolean
          description: Set to true to enable auto-renew, false to disable
      responses:
        '200':
          description: Auto-renew status successfully updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: Pass
                  Message:
                    type: string
                    example: AutoRenew status for SubscriptionId abc123 set to Yes
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: Fail
                  Message:
                    type: string
                    example: Internal server error
  /v1/subscription/getSubscriptions:
    get:
      summary: Get Subscriptions
      description: >-
        Retrieves subscriptions based on store, partner, customer, and brand
        filters
      tags:
        - Subscription
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store identifier
          example: store123
        - in: query
          name: partnerId
          required: true
          schema:
            type: string
          description: Partner identifier
          example: partner123
        - in: query
          name: customerId
          required: true
          schema:
            type: string
          description: Customer identifier
          example: customer123
        - in: query
          name: brandId
          required: true
          schema:
            type: string
          description: Brand identifier
          example: brand123
        - in: query
          name: subscriptionStatuses
          required: false
          schema:
            type: string
            default: active,suspended
          description: Comma-separated list of subscription statuses
          example: active,suspended
        - in: query
          name: expiryInDays
          required: false
          schema:
            type: string
            default: ALL
          description: Expiry filter in days
          example: ALL
      responses:
        '200':
          description: Successfully retrieved subscriptions
          content:
            application/json:
              schema:
                type: string
                description: >-
                  JSON string of subscription array (matching .NET response
                  format)
                example: '[{"SubscriptionId":"sub123","MaterialNo":"MAT123","Qty":10}]'
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  Errors:
                    type: array
                    items:
                      type: string
                    example:
                      - The 'StoreId' URL parameter is missing in the request.
                  StatusCode:
                    type: integer
                    example: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: Internal server error
  /v1/subscription/patchSubscription:
    post:
      summary: Update subscription status or quantity
      tags:
        - Subscription
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - customerId
                - subscriptionId
                - status
                - quantity
                - requestId
                - autoRenewEnabled
                - actualQty
                - updateType
                - partnerId
                - autoRenewToUpdate
              properties:
                storeId:
                  type: string
                  description: Store ID
                customerId:
                  type: string
                  description: Customer ID
                subscriptionId:
                  type: string
                  description: Subscription ID
                status:
                  type: string
                  description: Subscription status (e.g., Active, Cancelled)
                quantity:
                  type: integer
                  description: New subscription quantity
                requestId:
                  type: string
                  description: Unique request identifier
                autoRenewEnabled:
                  type: boolean
                  description: Whether auto-renew is enabled
                actualQty:
                  type: integer
                  description: Actual quantity to reflect
                updateType:
                  type: string
                  description: Type of update (e.g., status-only, quantity-only)
                partnerId:
                  type: string
                  description: Partner or reseller ID
                autoRenewToUpdate:
                  type: boolean
                  description: Whether auto-renew should be updated
            example:
              storeId: store-123
              customerId: cust-456
              subscriptionId: sub-789
              status: Active
              quantity: 5
              requestId: req-12345
              autoRenewEnabled: true
              actualQty: 5
              updateType: status-only
              partnerId: partner-001
              autoRenewToUpdate: true
      responses:
        '200':
          description: Subscription updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: SUCCESS
                  Message:
                    type: string
                    example: Subscription updated
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  errors:
                    type: array
                    items:
                      type: string
                    example:
                      - subscriptionId is required
                      - quantity must be a positive number
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  ErrorMessage:
                    type: string
                    example: Something went wrong
  /v1/subscription/terminateSubscription:
    post:
      summary: Terminate subscription by updating status
      description: >-
        Terminates a subscription by calling the patch subscription service with
        termination status
      tags:
        - Subscription
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - storeId
                - customerId
                - subscriptionId
                - status
              properties:
                storeId:
                  type: string
                  description: Store ID
                  example: store-123
                customerId:
                  type: string
                  description: Customer ID
                  example: cust-456
                subscriptionId:
                  type: string
                  description: Subscription ID to terminate
                  example: sub-789
                status:
                  type: string
                  description: Termination status (e.g., cancelled, suspended)
                  example: cancelled
                quantity:
                  type: number
                  description: Quantity to update
                  example: 0
                requestId:
                  type: string
                  description: Request ID for tracking
                  example: req-12345
                autoRenewEnabled:
                  type: boolean
                  description: Whether auto-renew is enabled
                  example: true
                actualQty:
                  type: number
                  description: Actual quantity to reflect
                  example: 0
                updateType:
                  type: string
                  description: Type of update (e.g., termination)
                  example: termination
                partnerId:
                  type: string
                  description: Partner or reseller ID
                  example: partner-123
                autoRenewToUpdate:
                  type: boolean
                  description: Whether auto-renew should be updated
                  example: true
            example:
              storeId: store-123
              customerId: cust-456
              subscriptionId: sub-789
              status: cancelled
              quantity: 0
              requestId: req-12345
              autoRenewEnabled: false
              actualQty: 0
              updateType: termination
              partnerId: partner-123
              autoRenewToUpdate: false
      responses:
        '200':
          description: Subscription terminated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: SUCCESS
                  ErrorMessage:
                    type: string
                    example: null
                  SubscriptionId:
                    type: string
                    example: sub-789
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  ErrorMessage:
                    type: string
                    example: Validation failed
                  Details:
                    type: array
                    items:
                      type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: FAIL
                  ErrorMessage:
                    type: string
                    example: Internal server error
  /v1/testSwagger:
    get:
      summary: Test Swagger response
      tags:
        - Test API
      responses:
        '200':
          description: Test response successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Hello Swagger
                  success:
                    type: boolean
                    example: true
  /v1/token/getCiscoToken:
    get:
      summary: Retrieve Cisco Access Token
      operationId: getCiscoToken
      description: >
        Returns a Cisco access token for a given storeId. This token is
        typically used to interact with Cisco's APIs.
      tags:
        - Token
      parameters:
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: The ID of the store requesting the Cisco token.
      responses:
        '200':
          description: Cisco token retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  token_type:
                    type: string
                  expires_in:
                    type: number
                  scope:
                    type: string
        '400':
          description: Missing or invalid storeId
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: storeId cannot be null or empty
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  error:
                    type: string
  /v1/token/getGoogleToken:
    get:
      summary: Retrieve Google Access Token
      description: >-
        Returns an access token for the given storeId by calling Google's OAuth
        token service.
      tags:
        - Token
      parameters:
        - in: query
          name: storeId
          required: true
          schema:
            type: string
          description: Store identifier to retrieve token credentials.
      responses:
        '200':
          description: Successfully retrieved Google token
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    description: The OAuth access token.
                  token_type:
                    type: string
                  expires_in:
                    type: integer
                  scope:
                    type: string
        '400':
          description: Missing or invalid storeId
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: storeId cannot be null or empty
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  error:
                    type: string
  /v1/token/getMicrosoftToken:
    get:
      summary: Get Microsoft Token
      tags:
        - Token
      parameters:
        - in: query
          name: storeId
          schema:
            type: string
          required: true
          description: Store ID
        - in: query
          name: brandId
          schema:
            type: string
          required: true
          description: Brand ID
      responses:
        '200':
          description: Successfully fetched token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  access_token:
                    type: string
                    example: eyJ0eXAiOiJKV1QiLCJh...
  /v1/transition/getNCEProductUpgrades:
    get:
      summary: Get NCE Product Upgrade Recommendations
      tags:
        - Transition
      parameters:
        - in: query
          name: customerId
          schema:
            type: string
          required: true
          description: Unique customer ID
        - in: query
          name: subscriptionId
          schema:
            type: string
          required: true
          description: Subscription ID for upgrade
        - in: query
          name: storeId
          schema:
            type: integer
          required: true
          description: Store ID associated with the request
        - in: query
          name: eligibilityType
          schema:
            type: string
            enum:
              - immediate
              - scheduled
          required: false
          description: Eligibility type for product upgrade
      responses:
        '200':
          description: Successfully retrieved product upgrade options
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: boolean
                    example: true
                  Message:
                    type: string
                    example: Product upgrade recommendations retrieved successfully
                  Items:
                    type: array
                    items:
                      type: object
                      properties:
                        ProductId:
                          type: string
                        ProductName:
                          type: string
                        UpgradeType:
                          type: string
                        EligibleFrom:
                          type: string
                          format: date
                        EligibleTo:
                          type: string
                          format: date
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                  example: customerId is required
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: boolean
                    example: false
                  Message:
                    type: string
                    example: Internal server error
                  Items:
                    type: array
                    items:
                      type: object
  /v1/transition/getSubscriptionsByCustomerIdAndMaterialId:
    get:
      summary: Get subscriptions by Customer ID and Material ID
      tags:
        - Transition
      parameters:
        - in: query
          name: customerId
          schema:
            type: string
          required: true
          description: Customer ID
        - in: query
          name: materialId
          schema:
            type: string
          required: true
          description: Material ID
      responses:
        '200':
          description: Subscriptions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: string
                    example: '1'
                  Message:
                    type: string
                    example: Subscriptions fetched successfully
                  Data:
                    type: array
                    items:
                      type: object
                      properties:
                        SubscriptionId:
                          type: string
                        CustomerId:
                          type: string
                        MaterialId:
                          type: string
                        Status:
                          type: string
                        CreatedDate:
                          type: string
                          format: date-time
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                  example: CustomerId is required
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: Internal server error
                  message:
                    type: string
                    example: Unexpected failure during processing
  /v1/transition/placeNCETransition:
    post:
      summary: Place NCE Transition request
      description: >-
        Places a New Commerce Experience (NCE) transition request for
        subscription upgrades/changes
      tags:
        - Transition
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - brandId
                - storeId
                - partnerId
                - customerId
                - fromSubscriptionId
                - fromMaterialId
                - quantity
                - toCatalogItemId
                - term
                - billFreq
              properties:
                brandId:
                  type: string
                  description: Brand ID
                  example: '14'
                storeId:
                  type: string
                  description: Store ID
                  example: store-123
                partnerId:
                  type: string
                  description: Partner ID
                  example: partner-456
                customerId:
                  type: string
                  description: Customer ID
                  example: cust-789
                fromSubscriptionId:
                  type: string
                  description: Source subscription ID to transition from
                  example: sub-from-123
                fromMaterialId:
                  type: string
                  description: 'Source material ID (format: product:sku)'
                  example: product:sku123
                fromCatalogItemId:
                  type: string
                  description: Source catalog item ID (optional, auto-populated)
                  example: catalog-from-123
                quantity:
                  type: number
                  description: Quantity for the transition
                  example: 5
                toSubscriptionId:
                  type: string
                  description: Target subscription ID (optional)
                  example: sub-to-456
                toCatalogItemId:
                  type: string
                  description: Target catalog item ID
                  example: catalog-to-456
                promotionId:
                  type: string
                  description: Promotion ID (optional)
                  example: promo-123
                term:
                  type: string
                  description: Subscription term
                  example: P1Y
                billFreq:
                  type: string
                  description: Billing frequency
                  example: Monthly
                updateType:
                  type: string
                  description: Type of update (optional)
                  example: upgrade
                subUpdateType:
                  type: string
                  description: Sub update type (optional)
                  example: license-upgrade
                unitPrice:
                  type: number
                  description: Unit price (optional)
                  example: 99.99
                taxRate:
                  type: number
                  description: Tax rate (optional)
                  example: 0.08
                promotionRate:
                  type: number
                  description: Promotion rate (optional)
                  example: 0.1
                segment:
                  type: string
                  description: Customer segment (optional)
                  example: enterprise
            example:
              brandId: string
              storeId: string
              partnerId: string
              customerId: string
              fromSubscriptionId: string
              fromMaterialId: string
              fromCatalogItemId: string
              quantity: 0
              toSubscriptionId: string
              toCatalogItemId: string
              promotionId: string
              term: string
              billFreq: string
              updateType: string
              subUpdateType: string
              unitPrice: 0
              taxRate: 0
              promotionRate: 0
              segment: string
      responses:
        '200':
          description: NCE Transition placed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: boolean
                    example: true
                  Message:
                    type: string
                    example: Transition request processed successfully
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
                  statusCode:
                    type: number
                    example: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: boolean
                    example: false
                  Message:
                    type: string
                    example: Internal server error
  /v1/transition/placeTransition:
    post:
      summary: Place Transition request (simplified version)
      description: >-
        Places a transition request for subscription changes without Microsoft
        validations
      tags:
        - Transition
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - brandId
                - storeId
                - partnerId
                - customerId
                - fromSubscriptionId
                - fromMaterialId
                - quantity
                - toCatalogItemId
                - term
                - billFreq
              properties:
                brandId:
                  type: string
                  description: Brand ID
                  example: string
                storeId:
                  type: string
                  description: Store ID
                  example: string
                partnerId:
                  type: string
                  description: Partner ID
                  example: string
                customerId:
                  type: string
                  description: Customer ID
                  example: string
                fromSubscriptionId:
                  type: string
                  description: Source subscription ID to transition from
                  example: string
                fromMaterialId:
                  type: string
                  description: Source material ID
                  example: string
                fromCatalogItemId:
                  type: string
                  description: >-
                    Source catalog item ID (optional, auto-populated from
                    fromMaterialId if not provided)
                  example: string
                quantity:
                  type: number
                  description: Quantity for the transition
                  example: 0
                toSubscriptionId:
                  type: string
                  description: Target subscription ID (optional)
                  example: string
                toCatalogItemId:
                  type: string
                  description: Target catalog item ID
                  example: string
                promotionId:
                  type: string
                  description: Promotion ID (optional)
                  example: string
                term:
                  type: string
                  description: Subscription term
                  example: string
                billFreq:
                  type: string
                  description: Billing frequency
                  example: string
                updateType:
                  type: string
                  description: Type of update (optional)
                  example: string
                subUpdateType:
                  type: string
                  description: Sub update type (optional)
                  example: string
                unitPrice:
                  type: number
                  description: Unit price (optional)
                  example: 0
                taxRate:
                  type: number
                  description: Tax rate (optional)
                  example: 0
                promotionRate:
                  type: number
                  description: Promotion rate (optional)
                  example: 0
                segment:
                  type: string
                  description: Customer segment (optional)
                  example: string
            example:
              brandId: string
              storeId: string
              partnerId: string
              customerId: string
              fromSubscriptionId: string
              fromMaterialId: string
              fromCatalogItemId: string
              quantity: 0
              toSubscriptionId: string
              toCatalogItemId: string
              promotionId: string
              term: string
              billFreq: string
              updateType: string
              subUpdateType: string
              unitPrice: 0
              taxRate: 0
              promotionRate: 0
              segment: string
      responses:
        '200':
          description: Transition placed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: boolean
                    example: true
                  Message:
                    type: string
                    example: Transition request processed successfully
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
                  statusCode:
                    type: number
                    example: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  Status:
                    type: boolean
                    example: false
                  Message:
                    type: string
                    example: Internal server error
components:
  schemas:
    GetContractResponse:
      type: object
      properties:
        ContractId:
          type: string
          example: string
        OrderId:
          type: string
          example: string
        SubscriptionId:
          type: string
          example: string
        MaterialId:
          type: string
          example: string
        MaterialNo:
          type: string
          example: string
        MaterialDesc:
          type: string
          example: string
        Qty:
          type: number
          example: -1
        MaterialType:
          type: string
          example: Base
        PlanType:
          type: string
          example: Annual
        StartDate:
          type: string
          format: date
          example: 29/07/2024
          description: Date in DD/MM/YYYY format
        EndDate:
          type: string
          format: date
          example: 28/07/2025
          description: Date in DD/MM/YYYY format
        PartnerCompany:
          type: string
          example: string
        CustomerCompany:
          type: string
          example: string
        ContractValue:
          type: number
          example: -21.2
        Status:
          type: string
          example: ACTIVE
        Brand:
          type: string
          example: Software Subscription
        ProvType:
          type: string
          example: AUTO
        CreateDate:
          type: string
          format: date
          example: 28/07/2025
          description: Date in DD/MM/YYYY format
        ListPrice:
          type: number
          example: 21.25
        UnitPrice:
          type: number
          example: 21.25
        StatusReason:
          type: string
          example: EOT
        Segment:
          type: string
          example: Commercial
        Term:
          type: string
          example: P1Y
        PaymentTermsCode:
          type: string
          example: R030
        PaymentTerms:
          type: string
          example: 45 Days  BG from Invoice date
tags: []
