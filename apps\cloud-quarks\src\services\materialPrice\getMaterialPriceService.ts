import logger from "../../utils/logger"
import { getSqlServerConnection } from "../../utils/sqlServerClient"

export interface MaterialPriceModel {
  status: string
  message: string
  costPrice: number
  listPrice: number
}

export async function getMaterialPriceService(
  storeId: string,
  materialId: string,
  brandName: string,
  billType: string,
  segment: string,
  term: string
): Promise<MaterialPriceModel> {
  logger.debug(
    `Inside service -> getMaterialPriceService with storeId=${storeId}, materialId=${materialId}, brandName=${brandName}, billType=${billType}, segment=${segment}, term=${term}`
  )

  const result = await getMaterialPriceFromDb(
    storeId,
    materialId,
    brandName,
    billType,
    segment,
    term
  )

  logger.debug(`Service returning: ${JSON.stringify(result)}`)

  return result
}

export async function getMaterialPriceFromDb(
  storeId: string,
  materialId: string,
  brandName: string,
  billType: string,
  segment: string,
  term: string
): Promise<{
  status: string
  message: string
  costPrice: number
  listPrice: number
}> {
  const procedure = "usp_get_material_price"

  logger.debug(
    `Calling stored procedure ${procedure} with storeId=${storeId}, materialId=${materialId}, brandName=${brandName}, billType=${billType}, segment=${segment}, term=${term}`
  )

  const pool = await getSqlServerConnection()

  const result = await pool
    .request()
    .input("iStoreId", storeId)
    .input("iMaterialId", materialId)
    .input("iBrandName", brandName)
    .input("iBillType", billType)
    .input("iSegment", segment)
    .input("iTerm", term)
    .execute(procedure)

  const rows = result.recordset as MaterialPriceModel[]
  logger.debug(
    `Stored proc ${procedure} returned rows: ${JSON.stringify(rows)}`
  )
  
  return rows[0]
}
