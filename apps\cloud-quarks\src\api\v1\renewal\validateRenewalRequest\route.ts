import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { validateRenewalRequestService } from "../../../../services/renewal/validateRenewalRequestService";

/**
 * @openapi
 * /v1/renewal/validateRenewalRequest:
 *   get:
 *     summary: Validate if a renewal request is valid for a given subscription
 *     tags:
 *       - Renewal
 *     parameters:
 *       - in: query
 *         name: subscriptionId
 *         schema:
 *           type: string
 *         required: true
 *         description: Subscription ID to validate for renewal
 *     responses:
 *       200:
 *         description: Renewal validation status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: PASS
 *                   description: "PASS if valid, FAIL otherwise"
 *                 Message:
 *                   type: string
 *                   example: Renewal is valid
 *       400:
 *         description: Invalid input provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: Invalid subscriptionId
 *       500:
 *         description: Internal server error or unexpected failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: Internal Server Error
 */

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const correlationId = getCorrelationId(req);
  const subscriptionId = req.query.subscriptionId as string;

  logger.info(
    `Entered into ValidateRenewalRequest API | CorrelationId: ${correlationId} | SubscriptionId: ${subscriptionId}`
  );

  if (!subscriptionId) {
    return res.status(400).json({
      Status: "FAIL",
      Message: "Invalid subscriptionId",
    });
  }

  try {
    const result = await validateRenewalRequestService(subscriptionId);
    logger.info(
      `Result from BL after all processes completed. Result's status is ${result.status}`
    );
    return res.status(200).json(result);
  } catch (error) {
    logger.error(
      `Error in ValidateRenewalRequest API | CorrelationId: ${correlationId} | ${error}`
    );
    return res.status(500).json({
      Status: "FAIL",
      Message: "Internal Server Error",
    });
  }
};
