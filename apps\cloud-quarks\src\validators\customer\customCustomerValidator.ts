import logger from "../../utils/logger";
import { MedusaRequest } from "@medusajs/framework";
export function validateCustomerUserQuery(
  brandId: string,
  storeId: string,
  tenantId: string
): string[] {
  const errors: string[] = []

  if (!brandId) {
    errors.push("The 'BrandId' URL parameter is missing in the request.")
  }
  if (!storeId) {
    errors.push("The 'StoreId' URL parameter is missing in the request.")
  }
  if (!tenantId) {
    errors.push("The 'tenantId' URL parameter is missing in the request.")
  }

  return errors
}

export function validateUpsertCustomer(dto: any): string[] {
  const errors: string[] = []

  // Rule: dto should not be null
  if (!dto) {
    errors.push("Invalid data structure")
    return errors
  }

  // Rule: Type should not be null
  if (!dto.type) {
    errors.push("Type cannot be null")
  }

  // Rule: Type must be either 'CREATE' or 'UPDATE'
  if (!isTypeValid(dto.type)) {
    errors.push("Invalid Type")
  }

  // Rule: If type is UPDATE, id must be present
  if (!isUpdateIdPresent(dto)) {
    errors.push("Id field is missing")
  }

  return errors
}

// Helper to normalize string
export function ComparableString(str?: string): string {
  return (str || "").trim().toUpperCase()
}

// Helper function to check if two strings are equal (case-insensitive)
export function isComparableStringEqual(str1: string | null | undefined, str2: string | null | undefined): boolean {
  return ComparableString(str1 || undefined) === ComparableString(str2 || undefined);
}

// Rule: Validates if Type is one of the accepted values
function isTypeValid(type?: string): boolean {
  const validTypes = ["CREATE", "UPDATE"]
  return validTypes.includes(ComparableString(type))
}

// Rule: For UPDATE type, id must be present
function isUpdateIdPresent(dto: any): boolean {
  if (ComparableString(dto.type) === "UPDATE") {
    return !!dto.id
  }
  return true
}



export function validateCustomerAgreementQuery(
  brandId: string,
  storeId: string,
  custId: string
): string[] {
  const errors: string[] = []

  if (!brandId) {
    errors.push("The 'BrandId' URL parameter is missing in the request.")
  }
  if (!storeId) {
    errors.push("The 'StoreId' URL parameter is missing in the request.")
  }
  if (!custId) {
    errors.push("The 'custId' URL parameter is missing in the request.")
  }

  return errors
}

export function validateGetCustomerByIdQuery(
  type: string,
  storeId: string,
  brandId: string,
  tenantId: string
): string[] {
  const errors: string[] = []

  logger.info(`Entered into validateGetCustomerByIdQuery with type-${type}, storeId-${storeId}, brandId-${brandId}, tenantId-${tenantId}`)

  // Validate type
  logger.info(`Validating query parameter 'type' with value: ${type}`)
  if (!type) {
    logger.info(`Validation failed for 'type': missing or empty`)
    errors.push("The 'type' URL parameter is missing in the request.")
  } else if (
    ComparableString(type) !== "VALIDATE-ID" &&
    ComparableString(type) !== "VALIDATE-DOMAIN"
  ) {
    logger.info(`Validation failed for 'type': invalid value '${type}'`)
    errors.push("The 'type' url parameter is wrong in the request.")
  }

  // Validate storeId
  logger.info(`Validating query parameter 'storeId' with value: ${storeId}`)
  if (!storeId) {
    logger.info(`Validation failed for 'storeId': missing or empty`)
    errors.push("The 'StoreId' URL parameter is missing in the request.")
  }

  // Validate brandId
  logger.info(`Validating query parameter 'brandId' with value: ${brandId}`)
  if (!brandId) {
    logger.info(`Validation failed for 'brandId': missing or empty`)
    errors.push("The 'BrandId' URL parameter is missing in the request.")
  }

  // Validate tenantId
  logger.info(`Validating query parameter 'tenantId' with value: ${tenantId}`)
  if (!tenantId) {
    logger.info(`Validation failed for 'tenantId': missing or empty`)
    errors.push("The 'TenantId' URL parameter is missing in the request.")
  }

  logger.info(`Validation completed. Total errors: ${errors.length} -> ${JSON.stringify(errors)}`)
  return errors
}

export function emptyValuelessParameterInRequest(
  keyValuePairs: Record<string, string>,
  query: Record<string, any>
) {
  for (const key in keyValuePairs) {
    if (query[key] === undefined || query[key] === null || query[key].toString().trim() === "") {
      keyValuePairs[key] = "";
    } else {
      keyValuePairs[key] = query[key].toString().trim();
    }
  }
}



export function validateGetDirectSignStatusOfCustAgreement(req: MedusaRequest): string[] {
  const query = req.query;

  let storeId = query.storeId as string;
  let custTenantId = query.custTenantId as string;

  const keyValuePairs: Record<string, string> = {
    storeId: storeId ?? "",
    custTenantId: custTenantId ?? "",
  };

  emptyValuelessParameterInRequest(keyValuePairs, query);

  storeId = keyValuePairs.storeId;
  custTenantId = keyValuePairs.custTenantId;

  const validationErrors: string[] = [];

  logger.info(
    `Validating query parameters. Final values: storeId-${storeId}, custTenantId-${custTenantId}`
  );

  if (!storeId) {
    logger.info(`Validation failed for storeId - missing`);
    validationErrors.push("The 'StoreId' URL parameter is missing in the request.");
  }

  if (!custTenantId) {
    logger.info(`Validation failed for custTenantId - missing`);
    validationErrors.push("The 'CustTenantId' URL parameter is missing in the request.");
  }

  logger.info(`Returning validation errors: ${JSON.stringify(validationErrors)}`);

  return validationErrors;
}


export function validateSetCustomerBillingInfo(dto: any): string[] {
  const errors: string[] = [];

  if (!dto) {
    errors.push("Invalid data structure");
    return errors;
  }

  if (!dto.Id) {
    errors.push("Id cannot be null or empty");
  }
  if (!dto.Email) {
    errors.push("Email cannot be null or empty");
  }
  if (!dto.Culture) {
    errors.push("Culture cannot be null or empty");
  }
  if (!dto.Language) {
    errors.push("Language cannot be null or empty");
  }
  if (!dto.CompanyName) {
    errors.push("CompanyName cannot be null or empty");
  }

  const addr = dto.DefaultAddress || {};
  if (!addr.Country) errors.push("Country cannot be null or empty");
  if (!addr.City) errors.push("City cannot be null or empty");
  if (!addr.AddressLine1) errors.push("AddressLine1 cannot be null or empty");
  if (!addr.FirstName) errors.push("FirstName cannot be null or empty");
  if (!addr.LastName) errors.push("LastName cannot be null or empty");

  const attrs = dto.Attributes || {};
  if (!attrs.Etag) errors.push("Etag cannot be null or empty");

  return errors;
}
