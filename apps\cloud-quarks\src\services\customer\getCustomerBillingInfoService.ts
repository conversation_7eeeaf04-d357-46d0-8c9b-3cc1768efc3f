import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { getMethod } from "../externalService/externalEndPointService";
import {
  getMicrosoftRequestHeader,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import logger from "../../utils/logger";
import { v4 as uuidv4 } from "uuid";
import { GetCustomerBillingInfoResponse } from "../../types/responses/customResponse";

export async function getCustomerBillingInfoService(
  req: MedusaRequest,
  brandId: string,
  storeId: string,
  tenantId: string
): Promise<GetCustomerBillingInfoResponse> {
  const errors: string[] = [];
  logger.info(`Calling getCustomerBillingInfoService service`);
  if (!brandId) errors.push("brandId is required");
  if (!storeId) errors.push("storeId is required");
  if (!tenantId) errors.push("tenantId is required");

  if (errors.length > 0) {
    return {
      id: "",
      email: "",
      culture: "",
      language: "",
      companyName: "",
      defaultAddress: null,
      links: null,
      attributes: null,
      firstName: "",
      lastName: "",
      code: 400,
      description: errors.join(", "),
      status: false,
    };
  }

  logger.info(
    `Calling GetStoreDetails for storeId=${storeId}, brandId=${brandId}`
  );
  const storeData = await getStoreDetails({ storeId, brandId });

  // Brand check
  const brandComparable = (storeData.brand || "")
    .replace(/\s+/g, "")
    .toUpperCase();
  if (brandComparable !== "MICROSOFT") {
    logger.info(
      `Brand is not MICROSOFT (${brandComparable}), skipping MS call`
    );
    return {
      id: "",
      email: "",
      culture: "",
      language: "",
      companyName: "",
      defaultAddress: null,
      links: null,
      attributes: null,
      firstName: "",
      lastName: "",
      code: -1,
      description: "Not Processed",
      status: false,
    };
  }

  // Log token request
  logger.info(
    `Preparing token request with storeData: ${JSON.stringify(storeData)}`
  );

  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "",
    redirect_uri: storeData.redirecturi || "",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  logger.info(`Token received: ${JSON.stringify(token)}`);

  if (!token || !token.access_token) {
    logger.error(`Token generation failed`);
    return {
      id: "",
      email: "",
      culture: "",
      language: "",
      companyName: "",
      defaultAddress: null,
      links: null,
      attributes: null,
      firstName: "",
      lastName: "",
      code: -1,
      description: "Get Token Error",
      status: false,
    };
  }

  const completeUrlWithParams =
    MicrosoftEndpoints.getCustomerBillingInfoUrl(tenantId);

  // Generate Microsoft headers
  const correlationId = uuidv4();
  const requestId = uuidv4();
  const headerList = getMicrosoftRequestHeader(
    token.access_token,
    correlationId,
    requestId
  );

  logger.info(
    `Prepared HeaderList for Microsoft call: ${JSON.stringify(headerList)}`
  );
  logger.info(`Hitting external endpoint with URL: ${completeUrlWithParams}`);

  const configModule = req.scope.resolve("configModule");

  const response = await getMethod(
    {
      url: completeUrlWithParams,
      headers: headerList,
      isVendorHit: true,
      module: "getCustomerBillingInfo",
    },
    configModule
  );

  logger.info(`Response from Microsoft: ${JSON.stringify(response)}`);
  logger.info(
    `Deserializing response.content into GetCustomerBillingInfoResponse`
  );
  // Parse response content
  const data = response?.content ? JSON.parse(response.content) : {};

  const parsed: GetCustomerBillingInfoResponse = {
    id: data.id || "",
    email: data.email || "",
    culture: data.culture || "",
    language: data.language || "",
    companyName: data.companyName || "",
    defaultAddress: data.defaultAddress || null,
    links: data.links || null,
    attributes: data.attributes || null,
    firstName: data.defaultAddress?.firstName || "",
    lastName: data.defaultAddress?.lastName || "",
    code: 0,
    description: "",
    status: true,
  };

  if (response?.isSuccessStatusCode && parsed.id) {
    parsed.code = 1;
    parsed.description = "Success";
  } else if (!response?.isSuccessStatusCode) {
    parsed.description = `${response.httpStatusCode} ${
      response.errorMessage || "Unknown Error"
    }`;
    parsed.status = false;
  }

  return parsed;
}
