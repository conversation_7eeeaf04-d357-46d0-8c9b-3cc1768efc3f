import { z } from "zod";
import logger from "../../utils/logger";

// Cisco Customer Validate DTO validation schema
export const CiscoCustomerValidateDtoSchema = z.object({
  custId: z.string().min(1, "Country code cannot be null or empty."),
  storeId: z.string().min(1, "Store Id cannot be null or empty."),
  partyName: z.string().min(1, "Party Name cannot null be null or empty."),
  addressLine1: z.string().min(1, "AddressLine1 cannot null be null or empty."),
  addressLine2: z.string().optional(),
  city: z.string().min(1, "City cannot null be null or empty."),
  state: z.string().min(1, "State cannot null be null or empty."),
  postalCode: z.string().min(1, "PostalCode cannot null be null or empty."),
  country: z.string().min(1, "Country cannot null be null or empty."),
  countryCode: z.string().min(1, "Country code cannot null be null or empty."),
  businessContactName: z.string().min(1, "BusinessContactName cannot be null or empty"),
  businessContactEmail: z.string()
    .min(1, "BusinessContactEmail cannot be null or empty")
    .email("BusinessContactEmail is not a valid email address"),
  businessContactNumber: z.string().min(1, "BusinessContactNumber cannot be null or empty"),
});

// Type definition for TypeScript
export type CiscoCustomerValidateDtoType = z.infer<typeof CiscoCustomerValidateDtoSchema>;

/**
 * Validate CiscoCustomerValidate request parameters
 */
export function validateCiscoCustomerValidateDto(dto: any): { isValid: boolean; errors: string[] } {
  logger.info(`Entered into validateCiscoCustomerValidateDto with dto: ${JSON.stringify(dto)}`);
  
  const errors: string[] = [];

  // Basic structure validation
  if (!dto) {
    errors.push("Invalid data structure");
    return { isValid: false, errors };
  }

  try {
    // Zod validation for all field validation
    CiscoCustomerValidateDtoSchema.parse(dto);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const zodErrors = error.errors.map(err => err.message);
      errors.push(...zodErrors);
    } else {
      errors.push("Invalid data structure");
    }
  }

  logger.info(`Validation completed with ${errors.length} errors: ${JSON.stringify(errors)}`);
  return { isValid: errors.length === 0, errors };
}
