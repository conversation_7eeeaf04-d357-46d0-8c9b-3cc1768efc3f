import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getMethod } from "../externalService/externalEndPointService";
import {
  createMicrosoftRequestHeaders,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import logger from "../../utils/logger";
import { IndirectResellerResponseItem } from "../../types/responses/customResponse";

export async function getIndirectResellerByIdService(
  req: MedusaRequest,
  storeId: string,
  indirectResellerId: string
): Promise<IndirectResellerResponseItem> {
  const loggerPrefix = "[GetIndirectResellerById]";

  logger.info(`${loggerPrefix} Getting store details for storeId: ${storeId}`);
  const storeData = await getStoreDetails({ storeId });
  logger.info(`${loggerPrefix} Store details: ${JSON.stringify(storeData)}`);

  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "",
    redirect_uri: storeData.redirecturi || "",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  logger.info(`${loggerPrefix} Token generated successfully`);

  const headerList = createMicrosoftRequestHeaders(token.access_token, storeId);
  logger.info(`${loggerPrefix} Header list: ${JSON.stringify(headerList)}`);

  const url = MicrosoftEndpoints.getIndirectResellerByIdUrl();
  logger.info(`${loggerPrefix} Complete URL: ${url}`);

  const configModule = req.scope.resolve("configModule");

  const response = await getMethod(
    {
      url,
      headers: headerList,
      isVendorHit: true,
      module: "GetIndirectResellerById",
    },
    configModule
  );

  logger.info(
    `${loggerPrefix} Response from external GET: ${JSON.stringify(response)}`
  );

  let indirectResellerInfo: IndirectResellerResponseItem = {
    id: "",
    name: "",
    relationshipType: "",
    state: "",
    mpnId: "",
    location: "",
    errorMessage: "",
  };

  if (response.isSuccessStatusCode) {
    logger.info(
      `${loggerPrefix} Successfully received response from external service`
    );
    try {
      const resellerResponse = JSON.parse(response.content);
      const items = resellerResponse.items || [];

      const matchingItem = items.find(
        (x: any) =>
          x.id?.trim().toUpperCase() ===
            indirectResellerId.trim().toUpperCase() &&
          x.mpnId &&
          x.name
      );

      if (matchingItem) {
        logger.info(`${loggerPrefix} Matching indirect reseller found`);
        indirectResellerInfo = {
          id: matchingItem.id,
          name: matchingItem.name,
          relationshipType: matchingItem.relationshipType,
          state: matchingItem.state,
          mpnId: matchingItem.mpnId,
          location: matchingItem.location,
          errorMessage: "",
        };
      } else {
        logger.info(`${loggerPrefix} No matching indirect reseller found`);
        indirectResellerInfo.errorMessage = "No indirect reseller found.";
      }
    } catch (err) {
      logger.error(`${loggerPrefix} Failed to parse response content: ${err}`);
      indirectResellerInfo.errorMessage =
        "Error parsing response from external service.";
    }
  } else {
    logger.info(`${loggerPrefix} Error received from external API`);
    try {
      const error = JSON.parse(response.content);
      indirectResellerInfo.errorMessage =
        error?.description || response.errorMessage || "Unknown error";
    } catch (err) {
      indirectResellerInfo.errorMessage =
        response.errorMessage || "Unknown error while parsing error response.";
    }
  }

  logger.info(
    `${loggerPrefix} Final response: ${JSON.stringify(indirectResellerInfo)}`
  );
  return indirectResellerInfo;
}
