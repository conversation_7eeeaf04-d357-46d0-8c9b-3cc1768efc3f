import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";

export interface StatusResponse {
  status: string;
  message: string;
}

export async function validateRenewalRequestService(
  subscriptionId: string
): Promise<StatusResponse> {
  logger.info(
    `Entered into BL validateRenewalRequestService with subscriptionId: ${subscriptionId}`
  );

  try {
    const response = await validateRenewalRequestDAL(subscriptionId);
    logger.info(
      `Response from DAL validateRenewalRequest: ${JSON.stringify(response)}`
    );
    return response;
  } catch (error: any) {
    logger.error(
      `Exception in validateRenewalRequestService | subscriptionId: ${subscriptionId} | Error: ${error.message}`
    );
    return {
      status: "FAIL",
      message: error.message,
    };
  }
}

export async function validateRenewalRequestDAL(
  subscriptionId: string
): Promise<StatusResponse> {
  logger.info(
    `Entered into DAL validateRenewalRequest method with subscriptionId: ${subscriptionId}`
  );

  const response: StatusResponse = {
    status: "FAIL",
    message: "Unexpected error",
  };

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    request.input("iSubscription", sql.VarChar, subscriptionId);
    request.output("oRetVal", sql.Bit);
    request.output("oRetMessage", sql.VarChar(500)); // updated to match SP

    logger.info(
      `Executing stored procedure spValidateSubscriptionRenewal with input: iSubscription=${subscriptionId}`
    );

    const result = await request.execute("spValidateSubscriptionRenewal");

    const status: boolean = result.output.oRetVal;
    const message: string = result.output.oRetMessage;

    response.status = status ? "PASS" : "FAIL";
    response.message = message;

    logger.info(
      `Stored procedure spValidateSubscriptionRenewal returned - Status: ${response.status}, Message: ${response.message}`
    );

    return response;
  } catch (error: any) {
    logger.error(
      `Error in validateRenewalRequestDAL | subscriptionId: ${subscriptionId} | message=${error.message}, stack=${error.stack}`
    );

    response.message = error.message;
    return response;
  }
}
