import { z } from "zod";
import logger from "../../utils/logger";

// Search Cisco Customer DTO validation schema
export const SearchCiscoCustomerDtoSchema = z.object({
  countryCode: z.string().min(1, "Country code cannot be null or empty."),
  name: z.string().min(1, "Name cannot be null or empty."),
  storeId: z.string().min(1, "Store Id cannot be null or empty."),
  pageSize: z.number().int().min(0, "Page size must be a non-negative integer."),
});

// Type definition for TypeScript
export type SearchCiscoCustomerDtoType = z.infer<typeof SearchCiscoCustomerDtoSchema>;

/**
 * Validate SearchCiscoCustomer request parameters
 */
export function validateSearchCiscoCustomerDto(dto: any): { isValid: boolean; errors: string[] } {
  logger.info(`Entered into validateSearchCiscoCustomerDto with dto: ${JSON.stringify(dto)}`);
  
  const errors: string[] = [];

  // Basic structure validation
  if (!dto) {
    errors.push("Invalid data structure");
    return { isValid: false, errors };
  }

  try {
    // Zod validation for all field validation
    SearchCiscoCustomerDtoSchema.parse(dto);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const zodErrors = error.errors.map(err => err.message);
      errors.push(...zodErrors);
    } else {
      errors.push("Invalid data structure");
    }
  }

  logger.info(`Validation completed with ${errors.length} errors: ${JSON.stringify(errors)}`);
  return { isValid: errors.length === 0, errors };
}
