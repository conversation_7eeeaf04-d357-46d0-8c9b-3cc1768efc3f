import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { getMethod } from "../externalService/externalEndPointService";
import {
  createMicrosoftRequestHeaders,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import {
  MicrosoftApiResponse,
} from "../../types/responses/customResponse";

export async function getAzureOperationDetailsService(
  req: MedusaRequest,
  storeId: string,
  brandId: string,
  operationId: string
): Promise<MicrosoftApiResponse> {
  const apiResponse: MicrosoftApiResponse = {
    message: "Success",
    statusCode: 200,
    isError: false,
  };

  try {
    // Get store details
    const storeDetails = await getStoreDetails({ storeId, brandId, credentialType:'GRAPH'  });

    if (!storeDetails) {
      logger.error(`Store details not found for storeId=${storeId}, brandId=${brandId}`);
      return {
        message: "Store details not found",
        statusCode: 400,
        isError: true,
      };
    }

    // Generate Microsoft token
    const token = await getMsToken({
      brand: storeDetails.brand || "",
      client_id: storeDetails.clientid || "",
      client_secret: storeDetails.clientsecret || "",
      grant_type: storeDetails.granttype || "",
      markValue: storeDetails.markvalue?.toString() || "",
      redirect_uri: storeDetails.redirecturi || "",
      refresh_token: storeDetails.token || "",
      resource: storeDetails.resource || "",
      store_domain: storeDetails.storedomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`Token generation failed`);
      return {
        message: "Error While Generating Token",
        statusCode: 500,
        isError: true,
      };
    }
    const operationUrl = MicrosoftEndpoints.getAzureOperationDetailsUrl(operationId)
    
    // Create Microsoft request headers
    const headerList = createMicrosoftRequestHeaders(
      token.access_token,
      storeId
    );

    console.log("headerList----->",headerList)

    logger.info(`Going to hit ExternalEndPoint getMethod method with url-${operationUrl}, headerlist-${JSON.stringify(headerList)}`);

    const configModule = req.scope.resolve("configModule");

    // Call Microsoft API
    const response = await getMethod(
      {
        url: operationUrl,
        headers: headerList,
        isVendorHit: true,
        module: "GetAzureOperationDetails",
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint getMethod is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      // Success response
      logger.info(`Received success response and deserializing response.Content-${response.content}`);

      let operationResponse;
      try {
        operationResponse = JSON.parse(response.content || "{}");
      } catch {
        operationResponse = response.content;
      }

      apiResponse.data = operationResponse;
    } else {
      // Error response
      logger.info(`Received errored response and deserialising response.ErrorMessage-${response.errorMessage}`);

      let errorMessage = "Unknown error";
      try {
        // Try to parse Microsoft error response
        const errorResponse = JSON.parse(response.content || response.errorMessage || "{}");
        errorMessage = errorResponse?.error?.message || errorResponse?.message || response.errorMessage || "Unknown error";
      } catch {
        errorMessage = response.errorMessage || "Unknown error";
      }

      apiResponse.message = errorMessage;
      apiResponse.statusCode = response.httpStatusCode;
      apiResponse.isError = true;
    }

    logger.info(`Going to return response from BL. ApiResponse-${JSON.stringify(apiResponse)}`);

    return apiResponse;
  } catch (error) {
    logger.error(`GetAzureOperationDetails Service Error:`, error);

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

    return {
      message: `Exception: ${errorMessage}`,
      statusCode: 422, // UnprocessableEntity
      isError: true,
    };
  }
}
