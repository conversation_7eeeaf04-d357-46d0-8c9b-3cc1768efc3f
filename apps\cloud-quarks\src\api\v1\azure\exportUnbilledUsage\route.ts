import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  AzureUnbilledUsageSchema,
  validateAzureUnbilledUsageStoreAndBrand
} from "../../../../validators/azure/azureUnbilledUsageValidator";
import { exportUnbilledUsageService } from "../../../../services/index";

/**
 * @openapi
 * /v1/azure/exportUnbilledUsage:
 *   post:
 *     summary: Export Azure unbilled usage data
 *     tags:
 *       - Azure
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - storeId
 *               - brandId
 *               - currencyCode
 *               - billingPeriod
 *               - attributeSet
 *             properties:
 *               storeId:
 *                 type: string
 *                 example: "store-123"
 *                 description: Store ID
 *               brandId:
 *                 type: string
 *                 example: "brand-456"
 *                 description: Brand ID
 *               currencyCode:
 *                 type: string
 *                 example: "USD"
 *                 description: Currency code for unbilled usage
 *               billingPeriod:
 *                 type: string
 *                 example: "2024-01"
 *                 description: Billing period for usage data
 *               attributeSet:
 *                 type: string
 *                 example: "usage-attributes"
 *                 description: Attribute set for usage data
 *           example:
 *             storeId: "string"
 *             brandId: "string"
 *             currencyCode: "string"
 *             billingPeriod: "string"
 *             attributeSet: "string"
 *     responses:
 *       200:
 *         description: Unbilled usage data exported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   description: Unbilled usage data
 *                 statusCode:
 *                   type: number
 *                   example: 200
 *                 isError:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *                 isError:
 *                   type: boolean
 *                   example: true
 *       422:
 *         description: Unprocessable entity
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: number
 *                   example: 422
 *                 isError:
 *                   type: boolean
 *                   example: true
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    const azureUnbilledUsageDto = req.body;

    logger.info(
      `Entered into ExportUnbilledUsage API with AzureUnbilledUsageDto | CorrelationId: ${correlationId} | DTO: ${JSON.stringify(azureUnbilledUsageDto)}`
    );

    // Validate DTO structure
    const parseResult = AzureUnbilledUsageSchema.safeParse(azureUnbilledUsageDto);
    logger.info(`Validations error count on AzureUnbilledUsageDto object is ${parseResult.success ? 0 : parseResult.error.errors.length}`);

    if (!parseResult.success) {
      const validationErrors = parseResult.error.errors.map((err: any) => err.message);
      const errorMessage = validationErrors.join(', ');

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }

    const validatedDto = parseResult.data;

    // Additional validation for store and brand
    const storeAndBrandErrors = validateAzureUnbilledUsageStoreAndBrand(validatedDto.storeId, validatedDto.brandId);
    if (storeAndBrandErrors.length > 0) {
      const errorMessage = storeAndBrandErrors.join(', ');

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }

    // Map DTO to request object (matching .NET mapping)
    const request = {
      CurrencyCode: validatedDto.currencyCode,
      BillingPeriod: validatedDto.billingPeriod,
      AttributeSet: validatedDto.attributeSet,
    };

    logger.info(`Mapped dto is: ${JSON.stringify(request)}`);

    // Call Azure service
    const serviceResult = await exportUnbilledUsageService(
      req,
      validatedDto.storeId,
      validatedDto.brandId,
      request
    );

    const { response: apiResponse, headers } = serviceResult;

    // Set response headers if provided
    if (headers) {
      Object.entries(headers).forEach(([key, value]) => {
        res.setHeader(key, value);
      });
    }

    logger.info(
      `Returning response from ExportUnbilledUsage API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(apiResponse)} | Headers: ${JSON.stringify(headers || {})}`
    );

    return res.status(apiResponse.StatusCode).json(apiResponse);
  } catch (exp) {
    const errorMessage = exp instanceof Error ? exp.message : "Unknown error occurred";

    const apiResponse = {
      isError: true,
      message: `Exception: ${errorMessage}`,
      statusCode: 422, // UnprocessableEntity
    };

    logger.error(
      `Error in ExportUnbilledUsage API | CorrelationId: ${correlationId} | Message: ${errorMessage} | StackTrace: ${exp instanceof Error ? exp.stack : 'No stack trace'}`
    );

    logger.error(
      `Returning response from ExportUnbilledUsage API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(apiResponse)}`
    );

    return res.status(422).json(apiResponse);
  }
}