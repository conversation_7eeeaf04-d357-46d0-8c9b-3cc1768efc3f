import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  MagentoResponse,
  GetSAPInvoiceDocumentDto,
  GetSAPInvoiceDocumentModel,
  S3Credentials,
} from "../../../../types/responses/customResponse";
import { validateGetSAPInvoiceDocument } from "../../../../validators/sap/getSAPInvoiceDocumentValidator";
import { getSAPInvoiceDocumentService } from "../../../../services/sap/sapInvoiceResponseService";

/**
 * @openapi
 * /v1/SAPInvoiceDocument/getSAPInvoiceDocument:
 *   get:
 *     summary: Get SAP Invoice Document
 *     tags:
 *       - SAP Invoice Document
 *     parameters:
 *       - in: query
 *         name: InvoiceNo
 *         required: true
 *         schema:
 *           type: string
 *           maxLength: 10
 *         description: Invoice number to retrieve document for
 *     responses:
 *       200:
 *         description: SAP Invoice Document retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: PASS
 *                 Message:
 *                   type: string
 *                   example: ""
 *                 Content:
 *                   type: string
 *                   description: Invoice document content from S3
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: InvoiceNo is required
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered into GetSAPInvoiceDocument API with invoiceNo ${JSON.stringify(
      req.query
    )} | CorrelationId: ${correlationId}`
  );

  const magentoResponse: MagentoResponse = {
    Status: "FAIL",
    Message: "Not Processed",
    Content: "",
  };

  try {
    // Extract query parameters and create DTO
    const getSAPInvoiceDocumentDto: GetSAPInvoiceDocumentDto = {
      InvoiceNo: (req.query.InvoiceNo as string) || "",
    };

    logger.info("Going to validate the invoiceNo");
    const validationErrors = await validateGetSAPInvoiceDocument(
      getSAPInvoiceDocumentDto
    );
    logger.info(
      `Validations error count on invoiceNo is ${validationErrors.length}`
    );

    if (validationErrors.length > 0) {
      logger.info("Going to extract errors from validationResult object");
      logger.info(`validation errors are ${JSON.stringify(validationErrors)}`);

      magentoResponse.Status = "FAIL";
      magentoResponse.Message = validationErrors.join(", ");
    } else {
      logger.info(
        `Going to map getSAPInvoiceDocument dto to model. getSAPInvoiceDocumentDto: ${JSON.stringify(
          getSAPInvoiceDocumentDto
        )}`
      );

      // Map DTO to Model (in this case they're identical)
      const getSAPInvoiceDocumentModel: GetSAPInvoiceDocumentModel = {
        InvoiceNo: getSAPInvoiceDocumentDto.InvoiceNo,
      };

      logger.info(
        `GetSAPInvoiceDocument model after mapping: ${JSON.stringify(
          getSAPInvoiceDocumentModel
        )}`
      );

      // Get S3 credentials from environment or configuration
      // Note: In production, these should come from a secure configuration service
      // const s3SAPInvoiceCredentials: S3Credentials = {
      //   AWS_ACCESS_KEY_ID: process.env.SAP_INVOICE_AWS_ACCESS_KEY_ID || "",
      //   AWS_SECRET_ACCESS_KEY: process.env.SAP_INVOICE_AWS_SECRET_ACCESS_KEY || "",
      //   AWS_REGION: process.env.SAP_INVOICE_AWS_REGION || "us-east-1",
      //   AWS_BUCKETNAME: process.env.SAP_INVOICE_AWS_BUCKETNAME || ""
      // };
      const s3SAPInvoiceCredentials: S3Credentials = {
        AWS_ACCESS_KEY_ID: "",
        AWS_SECRET_ACCESS_KEY: "",
        AWS_REGION: "us-east-1",
        AWS_BUCKETNAME: "",
      };

      logger.info(
        `Going to hit BL GetSAPInvoiceDocumentAsync method, getSAPInvoiceDocument model: ${JSON.stringify(
          getSAPInvoiceDocumentModel
        )}`
      );
      const result = await getSAPInvoiceDocumentService(
        getSAPInvoiceDocumentModel,
        s3SAPInvoiceCredentials
      );
      logger.info(`Result from BL full process is ${result}`);

      magentoResponse.Status = "PASS";
      magentoResponse.Content = result;
    }
  } catch (ex: any) {
    logger.error(
      `Entered Catch Block with, Error Message: ${ex.message}, Error StackTrace: ${ex.stack} | CorrelationId: ${correlationId}`
    );
    magentoResponse.Status = "FAIL";
    magentoResponse.Message = ex.message;
  }

  logger.info(
    `Going to return OK status from controller with response as: ${JSON.stringify(
      magentoResponse
    )} | CorrelationId: ${correlationId}`
  );
  return res.status(200).json(magentoResponse);
}
