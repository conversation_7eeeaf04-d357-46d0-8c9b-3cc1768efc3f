import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { saveIndirectResellerValidator } from "../../../../validators/indirectReseller/saveIndirectResellerValidator";
import { saveIndirectResellerService } from "../../../../services/indirectReseller/saveIndirectResellerService";

/**
 * @openapi
 * /v1/indirectReseller/saveIndirectReseller:
 *   post:
 *     summary: Save Indirect Reseller
 *     description: Saves or registers an indirect reseller with required partner, store, and reseller details.
 *     tags:
 *       - IndirectReseller
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - partnerId
 *               - storeId
 *               - vendor
 *               - irName
 *               - irId
 *               - mpnId
 *               - location
 *             properties:
 *               partnerId:
 *                 type: string
 *                 description: Unique identifier of the partner
 *                 example: "13331"
 *               storeId:
 *                 type: string
 *                 description: Unique identifier of the store
 *                 example: "AE-EN"
 *               vendor:
 *                 type: string
 *                 description: Vendor name associated with the reseller
 *                 example: "Microsoft"
 *               irName:
 *                 type: string
 *                 description: Name of the indirect reseller
 *                 example: "Clementine Technologies"
 *               irId:
 *                 type: string
 *                 description: Unique ID of the indirect reseller
 *                 example: "d1716a0f-835d-4210-94ea-c26b8c3f677b"
 *               mpnId:
 *                 type: string
 *                 description: Microsoft Partner Network ID
 *                 example: "6363276"
 *               location:
 *                 type: string
 *                 description: Location or country code of the indirect reseller
 *                 example: "AE"
 *     responses:
 *       200:
 *         description: Indirect reseller saved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: PASS
 *                 Message:
 *                   type: string
 *                   example: Indirect reseller saved successfully
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: partnerId is required, storeId is required
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: Internal Server Error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req)

  logger.info(
    `Entered into SaveIndirectReseller API | correlationId: ${correlationId} | Body: ${JSON.stringify(req.body)}`
  )

  const parseResult = saveIndirectResellerValidator.safeParse(req.body)

  if (!parseResult.success) {
    const errorMessages = parseResult.error.errors.map((e) => e.message)
    logger.info(`Validation failed: ${errorMessages.join(", ")}`)

    return res.status(400).json({
      Status: "FAIL",
      Message: errorMessages.join(", "),
    })
  }

  try {
    const dto = parseResult.data
    const response = await saveIndirectResellerService(dto)
    logger.info(`SaveIndirectResellerService response: ${JSON.stringify(response)}`)
    return res.status(200).json(response)
  } catch (err) {
    logger.error(`Error in SaveIndirectReseller API: ${err}`)
    return res.status(500).json({
      Status: "FAIL",
      Message: "Internal Server Error",
    })
  }
}
