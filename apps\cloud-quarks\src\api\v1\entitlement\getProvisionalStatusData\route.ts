import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  GetProvisionalStatusDataQuerySchema,
  validateGetProvisionalStatusDataQuery,
} from "../../../../validators/entitlement/getProvisionalStatusDataValidator";
import { getProvisionalStatusService } from "../../../../services/index";

/**
 * @openapi
 * /v1/entitlement/getProvisionalStatusData:
 *   get:
 *     summary: Get provisional status data for a customer order
 *     tags:
 *       - Entitlement
 *     parameters:
 *       - in: query
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Customer ID
 *         example: "cust-123"
 *       - in: query
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *         example: "order-456"
 *       - in: query
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Authorization token
 *         example: "bearer-token-xyz"
 *     responses:
 *       200:
 *         description: Provisional status retrieved successfully
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "Active"
 *               description: The provisional status of the order
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *                 isError:
 *                   type: boolean
 *                   example: true
 *       422:
 *         description: Service error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: number
 *                   example: 422
 *                 isError:
 *                   type: boolean
 *                   example: true
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    const { customerId, orderId, token } = req.query as Record<string, string>;

    logger.info(
      `Entered into GetProvisionalStatusData API with customerId - ${customerId}, orderId ${orderId} | CorrelationId: ${correlationId}`
    );

    // Validate query parameters
    const queryValidationErrors = validateGetProvisionalStatusDataQuery(
      customerId,
      orderId,
      token
    );
    if (queryValidationErrors.length > 0) {
      const errorMessage = queryValidationErrors.join(", ");

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(
        `Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(
          apiResponse
        )}`
      );
      return res.status(400).json(apiResponse);
    }

    // Additional validation using Zod schema
    const parseResult = GetProvisionalStatusDataQuerySchema.safeParse({
      customerId,
      orderId,
      token,
    });
    if (!parseResult.success) {
      const validationErrors = parseResult.error.errors.map(
        (err: any) => err.message
      );
      const errorMessage = validationErrors.join(", ");

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(
        `Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(
          apiResponse
        )}`
      );
      return res.status(400).json(apiResponse);
    }

    // Call service to get provisional status
    const provisionalStatus = await getProvisionalStatusService(
      req,
      customerId,
      orderId,
      token
    );

    logger.info(
      `Returning response from GetProvisionalStatusData API | CorrelationId: ${correlationId} | Status: ${provisionalStatus}`
    );

    // if (!provisionalStatus) {
    //   logger.warn(
    //     `GetProvisionalStatus returned empty or failed. CorrelationId: ${correlationId}`
    //   );
    //   return res.status(502).json({
    //     success: false,
    //     message: "Failed to retrieve provisional status",
    //     correlationId,
    //   });
    // }
    // Return the status as plain text (matching .NET behavior)
    return res.status(200).send({provisionalStatus});
  } catch (exp) {
    const errorMessage =
      exp instanceof Error ? exp.message : "Unknown error occurred";

    logger.error(
      `ERROR in GetProvisionalStatus | CorrelationId: ${correlationId} | ErrorDetail: ${errorMessage} | StackTrace: ${
        exp instanceof Error ? exp.stack : "No stack trace"
      }`
    );

    const apiResponse = {
      isError: true,
      message: `Exception: ${errorMessage}`,
      statusCode: 422, // UnprocessableEntity
    };

    logger.error(
      `Returning error response from GetProvisionalStatusData API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(
        apiResponse
      )}`
    );

    return res.status(422).json(apiResponse);
  }
}
