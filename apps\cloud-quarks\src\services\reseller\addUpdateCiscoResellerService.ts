import logger from "../../utils/logger";
import { ResellerDto } from "../../validators/reseller/addUpdateCiscoResellerValidator";
import sql from "mssql"
import { getSqlServerConnection } from "../../utils/sqlServerClient"

export type StatusResponse = {
  status: string;
  message: string;
};

export type StatusResponseType = StatusResponse & {
  actionType: string;
};

export const addUpdateCiscoResellerService = async (
  input: ResellerDto,
  correlationId: string
): Promise<StatusResponseType> => {
  logger.info(
    `Entered into addUpdateCiscoResellerService | CorrelationId: ${correlationId} | Input: ${JSON.stringify(input)}`
  );

  const result = await addUpdateCiscoResellerDal(input);

  logger.info(
    `Response from DAL | CorrelationId: ${correlationId} | Result: ${JSON.stringify(result)}`
  );

  return result
};

export async function addUpdateCiscoResellerDal(
  model: ResellerDto
): Promise<StatusResponseType> {
  logger.info(
    `Calling stored proc [spSaveCiscoReseller] with payload: ${JSON.stringify(model)}`
  )

  const pool = await getSqlServerConnection()
  const request = pool.request()

  // Input Parameters
  request.input("iStoreId", sql.VarChar(50), model.StoreId)
  request.input("iPartnerId", sql.VarChar(50), model.PartnerId)
  request.input("iPartyName", sql.NVarChar(200), model.PartyName)
  request.input("iAddressLine1", sql.NVarChar(200), model.AddressLine1)
  request.input("iAddressLine2", sql.NVarChar(200), model.AddressLine2)
  request.input("iCity", sql.NVarChar(100), model.City)
  request.input("iState", sql.NVarChar(100), model.State)
  request.input("iPostalCode", sql.VarChar(20), model.PostalCode)
  request.input("iCountry", sql.NVarChar(100), model.Country)
  request.input("iBusinessContactName", sql.NVarChar(100), model.BusinessContactName)
  request.input("iBusinessContactEmail", sql.VarChar(100), model.BusinessContactEmail)
  request.input("iBusinessContactNo", sql.VarChar(20), model.BusinessContactNumber)
  request.input("iAccountNo", sql.VarChar(50), model.AccountNumber)
  request.input("iIsActive", sql.Bit, model.IsActive ?? false)

  // Output Parameters
  request.output("oRetValue", sql.VarChar(20))
  request.output("oActionType", sql.VarChar(20))
  request.output("oMessage", sql.VarChar(200))

  try {
    const result = await request.execute("spSaveCiscoReseller")

    const status = result.output.oRetValue
    const message = result.output.oMessage
    const actionType = result.output.oActionType

    logger.info(
      `Stored Proc [spSaveCiscoReseller] Result - Status: ${status}, Message: ${message}, ActionType: ${actionType}`
    )

    return {
      status: status,
      message: message,
      actionType: actionType,
    }
  } catch (error) {
    logger.error("Error executing stored procedure [spSaveCiscoReseller]:", error)
    throw error
  }
}


