import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from 'mssql';


type StatusResponse = {
  status: string
  message: string
}

export interface SaveIndirectResellerModel {
  partnerId: string;
  storeId: string;
  vendor: string;
  irName: string;
  irId: string;
  mpnId: string;
  location: string;
}


export async function saveIndirectResellerService(
  input: SaveIndirectResellerModel
): Promise<StatusResponse> {
  logger.info(
    `Entered into saveIndirectResellerService with input: ${JSON.stringify(input)}`
  );

  const pool = await getSqlServerConnection();
  const request = pool.request();

  request.input("PartnerId", input.partnerId);
  request.input("StoreId", input.storeId);
  request.input("Vendor", input.vendor);
  request.input("IRName", input.irName);
  request.input("IRId", input.irId);
  request.input("MpnId", input.mpnId);
  request.input("Location", input.location);

  request.output("RetVal", sql.VarChar(200));
  request.output("Output", sql.VarChar(2000));

  logger.info(
    `Going to hit DB with proc name: spSaveIndirectReseller and parameters: ${JSON.stringify({
      PartnerId: input.partnerId,
      StoreId: input.storeId,
      Vendor: input.vendor,
      IRName: input.irName,
      IRId: input.irId,
      MpnId: input.mpnId,
      Location: input.location,
    })}`
  );

  const result = await request.execute("spSaveIndirectReseller");

  const status = result.output?.RetVal || "FAIL";
  const message = result.output?.Output || "No message returned";

  logger.info(
    `Proc ran successfully and returned: RetVal=${status}, Message=${message}`
  );

  return {
    status: status,
    message: message,
  };
}
